{"ast": null, "code": "/**\r\n * API Utility Functions\r\n *\r\n * This module provides standardized functions for making API requests.\r\n * It handles authentication, error handling, and URL formatting.\r\n */\n\nimport axios from \"axios\";\nimport { getApiUrl } from \"./apiConfig\";\n\n/**\r\n * Token key used for authentication in localStorage\r\n */\nconst TOKEN_KEY = \"merakihr-token\";\n\n/**\r\n * Get the authentication token from localStorage\r\n *\r\n * @returns {string|null} The authentication token or null if not found\r\n */\nconst getAuthToken = () => localStorage.getItem(TOKEN_KEY);\n\n/**\r\n * Create standard headers for API requests\r\n *\r\n * @param {string} contentType - The content type for the request\r\n * @returns {Object} Headers object with authorization and content type\r\n */\nconst createHeaders = (contentType = 'application/json') => {\n  const token = getAuthToken();\n  return {\n    Authorization: token ? `Bearer ${token}` : undefined,\n    'Content-Type': contentType\n  };\n};\n\n/**\r\n * Format a URL to ensure it has the correct base API URL\r\n *\r\n * @param {string} url - The URL to format\r\n * @returns {string} The formatted URL with the correct base\r\n */\nconst formatUrl = url => {\n  return url.includes('http://') || url.includes('https://') ? url : getApiUrl(url);\n};\n\n/**\r\n * Make a GET request to the API\r\n *\r\n * @param {string} url - The endpoint URL\r\n * @param {Object} query - Query parameters\r\n * @returns {Promise<Object>} The response data\r\n */\nexport async function get(url, query) {\n  let queryString = \"\";\n  if (query) {\n    queryString = `?${new URLSearchParams(query).toString()}`;\n  }\n  const fullUrl = formatUrl(url);\n  const headers = createHeaders();\n  try {\n    return await axios.get(fullUrl + queryString, {\n      headers\n    });\n  } catch (error) {\n    var _error$response, _error$response$data, _error$response2, _error$response3;\n    // Handle error and provide more context\n    const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message;\n    const enhancedError = new Error(`GET request failed: ${errorMessage}`);\n    enhancedError.originalError = error;\n    enhancedError.status = (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status;\n    enhancedError.data = (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data;\n    throw enhancedError;\n  }\n}\n\n/**\r\n * Make a POST request to the API\r\n *\r\n * @param {string} url - The endpoint URL\r\n * @param {Object} data - The data to send\r\n * @returns {Promise<Object>} The response data\r\n */\nexport async function post(url, data) {\n  const formData = new FormData();\n  Object.keys(data).forEach(key => formData.append(key, data[key]));\n  const fullUrl = formatUrl(url);\n  const headers = createHeaders('multipart/form-data');\n  try {\n    return await axios.post(fullUrl, formData, {\n      headers\n    });\n  } catch (error) {\n    var _error$response4, _error$response4$data, _error$response5, _error$response6;\n    const errorMessage = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || error.message;\n    const enhancedError = new Error(`POST request failed: ${errorMessage}`);\n    enhancedError.originalError = error;\n    enhancedError.status = (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status;\n    enhancedError.data = (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data;\n    throw enhancedError;\n  }\n}\n\n/**\r\n * Make a PATCH request to the API\r\n *\r\n * @param {string} url - The endpoint URL\r\n * @param {Object} data - The data to send\r\n * @returns {Promise<Object>} The response data\r\n */\nexport async function patch(url, data) {\n  const formData = new FormData();\n\n  // Properly handle different data types when adding to FormData\n  Object.keys(data).forEach(key => {\n    // Handle null or undefined\n    if (data[key] === null || data[key] === undefined) {\n      formData.append(key, '');\n    }\n    // Handle numeric values explicitly to ensure they're sent as numbers\n    else if (typeof data[key] === 'number') {\n      formData.append(key, data[key].toString());\n    }\n    // Handle boolean values\n    else if (typeof data[key] === 'boolean') {\n      formData.append(key, data[key] ? 'true' : 'false');\n    }\n    // Handle objects (like workSchedule) by JSON stringifying them\n    else if (typeof data[key] === 'object' && data[key] !== null && !(data[key] instanceof File)) {\n      formData.append(key, JSON.stringify(data[key]));\n    }\n    // Handle all other cases (strings, files, etc.)\n    else {\n      formData.append(key, data[key]);\n    }\n  });\n\n  // Log the form data for debugging\n  console.log('Sending data in patch request:', Object.fromEntries(formData.entries()));\n  const fullUrl = formatUrl(url);\n  const headers = createHeaders('multipart/form-data');\n  try {\n    return await axios.patch(fullUrl, formData, {\n      headers\n    });\n  } catch (error) {\n    var _error$response7, _error$response7$data, _error$response8, _error$response9;\n    const errorMessage = ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || error.message;\n    const enhancedError = new Error(`PATCH request failed: ${errorMessage}`);\n    enhancedError.originalError = error;\n    enhancedError.status = (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.status;\n    enhancedError.data = (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data;\n    throw enhancedError;\n  }\n}\n\n/**\r\n * Make a DELETE request to the API\r\n *\r\n * @param {string} url - The endpoint URL\r\n * @returns {Promise<Object>} The response data\r\n */\nexport async function del(url) {\n  const fullUrl = formatUrl(url);\n  const headers = createHeaders();\n  try {\n    return await axios.delete(fullUrl, {\n      headers\n    });\n  } catch (error) {\n    var _error$response0, _error$response0$data, _error$response1, _error$response10;\n    const errorMessage = ((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : (_error$response0$data = _error$response0.data) === null || _error$response0$data === void 0 ? void 0 : _error$response0$data.message) || error.message;\n    const enhancedError = new Error(`DELETE request failed: ${errorMessage}`);\n    enhancedError.originalError = error;\n    enhancedError.status = (_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : _error$response1.status;\n    enhancedError.data = (_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : _error$response10.data;\n    throw enhancedError;\n  }\n}", "map": {"version": 3, "names": ["axios", "getApiUrl", "TOKEN_KEY", "getAuthToken", "localStorage", "getItem", "createHeaders", "contentType", "token", "Authorization", "undefined", "formatUrl", "url", "includes", "get", "query", "queryString", "URLSearchParams", "toString", "fullUrl", "headers", "error", "_error$response", "_error$response$data", "_error$response2", "_error$response3", "errorMessage", "response", "data", "message", "enhancedError", "Error", "originalError", "status", "post", "formData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "append", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "patch", "File", "JSON", "stringify", "console", "log", "fromEntries", "entries", "_error$response7", "_error$response7$data", "_error$response8", "_error$response9", "del", "delete", "_error$response0", "_error$response0$data", "_error$response1", "_error$response10"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/utils/api.js"], "sourcesContent": ["/**\r\n * API Utility Functions\r\n *\r\n * This module provides standardized functions for making API requests.\r\n * It handles authentication, error handling, and URL formatting.\r\n */\r\n\r\nimport axios from \"axios\";\r\nimport { getApiUrl } from \"./apiConfig\";\r\n\r\n/**\r\n * Token key used for authentication in localStorage\r\n */\r\nconst TOKEN_KEY = \"merakihr-token\";\r\n\r\n/**\r\n * Get the authentication token from localStorage\r\n *\r\n * @returns {string|null} The authentication token or null if not found\r\n */\r\nconst getAuthToken = () => localStorage.getItem(TOKEN_KEY);\r\n\r\n/**\r\n * Create standard headers for API requests\r\n *\r\n * @param {string} contentType - The content type for the request\r\n * @returns {Object} Headers object with authorization and content type\r\n */\r\nconst createHeaders = (contentType = 'application/json') => {\r\n    const token = getAuthToken();\r\n    return {\r\n        Authorization: token ? `Bearer ${token}` : undefined,\r\n        'Content-Type': contentType\r\n    };\r\n};\r\n\r\n/**\r\n * Format a URL to ensure it has the correct base API URL\r\n *\r\n * @param {string} url - The URL to format\r\n * @returns {string} The formatted URL with the correct base\r\n */\r\nconst formatUrl = (url) => {\r\n    return url.includes('http://') || url.includes('https://') ? url : getApiUrl(url);\r\n};\r\n\r\n/**\r\n * Make a GET request to the API\r\n *\r\n * @param {string} url - The endpoint URL\r\n * @param {Object} query - Query parameters\r\n * @returns {Promise<Object>} The response data\r\n */\r\nexport async function get(url, query) {\r\n    let queryString = \"\";\r\n\r\n    if (query) {\r\n        queryString = `?${new URLSearchParams(query).toString()}`;\r\n    }\r\n\r\n    const fullUrl = formatUrl(url);\r\n    const headers = createHeaders();\r\n\r\n    try {\r\n        return await axios.get(fullUrl + queryString, { headers });\r\n    } catch (error) {\r\n        // Handle error and provide more context\r\n        const errorMessage = error.response?.data?.message || error.message;\r\n        const enhancedError = new Error(`GET request failed: ${errorMessage}`);\r\n        enhancedError.originalError = error;\r\n        enhancedError.status = error.response?.status;\r\n        enhancedError.data = error.response?.data;\r\n        throw enhancedError;\r\n    }\r\n}\r\n\r\n/**\r\n * Make a POST request to the API\r\n *\r\n * @param {string} url - The endpoint URL\r\n * @param {Object} data - The data to send\r\n * @returns {Promise<Object>} The response data\r\n */\r\nexport async function post(url, data) {\r\n    const formData = new FormData();\r\n    Object.keys(data).forEach(key => formData.append(key, data[key]));\r\n\r\n    const fullUrl = formatUrl(url);\r\n    const headers = createHeaders('multipart/form-data');\r\n\r\n    try {\r\n        return await axios.post(fullUrl, formData, { headers });\r\n    } catch (error) {\r\n        const errorMessage = error.response?.data?.message || error.message;\r\n        const enhancedError = new Error(`POST request failed: ${errorMessage}`);\r\n        enhancedError.originalError = error;\r\n        enhancedError.status = error.response?.status;\r\n        enhancedError.data = error.response?.data;\r\n        throw enhancedError;\r\n    }\r\n}\r\n\r\n/**\r\n * Make a PATCH request to the API\r\n *\r\n * @param {string} url - The endpoint URL\r\n * @param {Object} data - The data to send\r\n * @returns {Promise<Object>} The response data\r\n */\r\nexport async function patch(url, data) {\r\n    const formData = new FormData();\r\n\r\n    // Properly handle different data types when adding to FormData\r\n    Object.keys(data).forEach(key => {\r\n        // Handle null or undefined\r\n        if (data[key] === null || data[key] === undefined) {\r\n            formData.append(key, '');\r\n        }\r\n        // Handle numeric values explicitly to ensure they're sent as numbers\r\n        else if (typeof data[key] === 'number') {\r\n            formData.append(key, data[key].toString());\r\n        }\r\n        // Handle boolean values\r\n        else if (typeof data[key] === 'boolean') {\r\n            formData.append(key, data[key] ? 'true' : 'false');\r\n        }\r\n        // Handle objects (like workSchedule) by JSON stringifying them\r\n        else if (typeof data[key] === 'object' && data[key] !== null && !(data[key] instanceof File)) {\r\n            formData.append(key, JSON.stringify(data[key]));\r\n        }\r\n        // Handle all other cases (strings, files, etc.)\r\n        else {\r\n            formData.append(key, data[key]);\r\n        }\r\n    });\r\n\r\n    // Log the form data for debugging\r\n    console.log('Sending data in patch request:', Object.fromEntries(formData.entries()));\r\n\r\n    const fullUrl = formatUrl(url);\r\n    const headers = createHeaders('multipart/form-data');\r\n\r\n    try {\r\n        return await axios.patch(fullUrl, formData, { headers });\r\n    } catch (error) {\r\n        const errorMessage = error.response?.data?.message || error.message;\r\n        const enhancedError = new Error(`PATCH request failed: ${errorMessage}`);\r\n        enhancedError.originalError = error;\r\n        enhancedError.status = error.response?.status;\r\n        enhancedError.data = error.response?.data;\r\n        throw enhancedError;\r\n    }\r\n}\r\n\r\n/**\r\n * Make a DELETE request to the API\r\n *\r\n * @param {string} url - The endpoint URL\r\n * @returns {Promise<Object>} The response data\r\n */\r\nexport async function del(url) {\r\n    const fullUrl = formatUrl(url);\r\n    const headers = createHeaders();\r\n\r\n    try {\r\n        return await axios.delete(fullUrl, { headers });\r\n    } catch (error) {\r\n        const errorMessage = error.response?.data?.message || error.message;\r\n        const enhancedError = new Error(`DELETE request failed: ${errorMessage}`);\r\n        enhancedError.originalError = error;\r\n        enhancedError.status = error.response?.status;\r\n        enhancedError.data = error.response?.data;\r\n        throw enhancedError;\r\n    }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,aAAa;;AAEvC;AACA;AACA;AACA,MAAMC,SAAS,GAAG,gBAAgB;;AAElC;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGA,CAAA,KAAMC,YAAY,CAACC,OAAO,CAACH,SAAS,CAAC;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,aAAa,GAAGA,CAACC,WAAW,GAAG,kBAAkB,KAAK;EACxD,MAAMC,KAAK,GAAGL,YAAY,CAAC,CAAC;EAC5B,OAAO;IACHM,aAAa,EAAED,KAAK,GAAG,UAAUA,KAAK,EAAE,GAAGE,SAAS;IACpD,cAAc,EAAEH;EACpB,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,SAAS,GAAIC,GAAG,IAAK;EACvB,OAAOA,GAAG,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAGD,GAAG,GAAGX,SAAS,CAACW,GAAG,CAAC;AACrF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeE,GAAGA,CAACF,GAAG,EAAEG,KAAK,EAAE;EAClC,IAAIC,WAAW,GAAG,EAAE;EAEpB,IAAID,KAAK,EAAE;IACPC,WAAW,GAAG,IAAI,IAAIC,eAAe,CAACF,KAAK,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAE;EAC7D;EAEA,MAAMC,OAAO,GAAGR,SAAS,CAACC,GAAG,CAAC;EAC9B,MAAMQ,OAAO,GAAGd,aAAa,CAAC,CAAC;EAE/B,IAAI;IACA,OAAO,MAAMN,KAAK,CAACc,GAAG,CAACK,OAAO,GAAGH,WAAW,EAAE;MAAEI;IAAQ,CAAC,CAAC;EAC9D,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,gBAAA;IACZ;IACA,MAAMC,YAAY,GAAG,EAAAJ,eAAA,GAAAD,KAAK,CAACM,QAAQ,cAAAL,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBM,IAAI,cAAAL,oBAAA,uBAApBA,oBAAA,CAAsBM,OAAO,KAAIR,KAAK,CAACQ,OAAO;IACnE,MAAMC,aAAa,GAAG,IAAIC,KAAK,CAAC,uBAAuBL,YAAY,EAAE,CAAC;IACtEI,aAAa,CAACE,aAAa,GAAGX,KAAK;IACnCS,aAAa,CAACG,MAAM,IAAAT,gBAAA,GAAGH,KAAK,CAACM,QAAQ,cAAAH,gBAAA,uBAAdA,gBAAA,CAAgBS,MAAM;IAC7CH,aAAa,CAACF,IAAI,IAAAH,gBAAA,GAAGJ,KAAK,CAACM,QAAQ,cAAAF,gBAAA,uBAAdA,gBAAA,CAAgBG,IAAI;IACzC,MAAME,aAAa;EACvB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeI,IAAIA,CAACtB,GAAG,EAAEgB,IAAI,EAAE;EAClC,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BC,MAAM,CAACC,IAAI,CAACV,IAAI,CAAC,CAACW,OAAO,CAACC,GAAG,IAAIL,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAEZ,IAAI,CAACY,GAAG,CAAC,CAAC,CAAC;EAEjE,MAAMrB,OAAO,GAAGR,SAAS,CAACC,GAAG,CAAC;EAC9B,MAAMQ,OAAO,GAAGd,aAAa,CAAC,qBAAqB,CAAC;EAEpD,IAAI;IACA,OAAO,MAAMN,KAAK,CAACkC,IAAI,CAACf,OAAO,EAAEgB,QAAQ,EAAE;MAAEf;IAAQ,CAAC,CAAC;EAC3D,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAqB,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;IACZ,MAAMnB,YAAY,GAAG,EAAAgB,gBAAA,GAAArB,KAAK,CAACM,QAAQ,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAIR,KAAK,CAACQ,OAAO;IACnE,MAAMC,aAAa,GAAG,IAAIC,KAAK,CAAC,wBAAwBL,YAAY,EAAE,CAAC;IACvEI,aAAa,CAACE,aAAa,GAAGX,KAAK;IACnCS,aAAa,CAACG,MAAM,IAAAW,gBAAA,GAAGvB,KAAK,CAACM,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgBX,MAAM;IAC7CH,aAAa,CAACF,IAAI,IAAAiB,gBAAA,GAAGxB,KAAK,CAACM,QAAQ,cAAAkB,gBAAA,uBAAdA,gBAAA,CAAgBjB,IAAI;IACzC,MAAME,aAAa;EACvB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAegB,KAAKA,CAAClC,GAAG,EAAEgB,IAAI,EAAE;EACnC,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;EAE/B;EACAC,MAAM,CAACC,IAAI,CAACV,IAAI,CAAC,CAACW,OAAO,CAACC,GAAG,IAAI;IAC7B;IACA,IAAIZ,IAAI,CAACY,GAAG,CAAC,KAAK,IAAI,IAAIZ,IAAI,CAACY,GAAG,CAAC,KAAK9B,SAAS,EAAE;MAC/CyB,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAE,EAAE,CAAC;IAC5B;IACA;IAAA,KACK,IAAI,OAAOZ,IAAI,CAACY,GAAG,CAAC,KAAK,QAAQ,EAAE;MACpCL,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAEZ,IAAI,CAACY,GAAG,CAAC,CAACtB,QAAQ,CAAC,CAAC,CAAC;IAC9C;IACA;IAAA,KACK,IAAI,OAAOU,IAAI,CAACY,GAAG,CAAC,KAAK,SAAS,EAAE;MACrCL,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAEZ,IAAI,CAACY,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;IACtD;IACA;IAAA,KACK,IAAI,OAAOZ,IAAI,CAACY,GAAG,CAAC,KAAK,QAAQ,IAAIZ,IAAI,CAACY,GAAG,CAAC,KAAK,IAAI,IAAI,EAAEZ,IAAI,CAACY,GAAG,CAAC,YAAYO,IAAI,CAAC,EAAE;MAC1FZ,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAEQ,IAAI,CAACC,SAAS,CAACrB,IAAI,CAACY,GAAG,CAAC,CAAC,CAAC;IACnD;IACA;IAAA,KACK;MACDL,QAAQ,CAACM,MAAM,CAACD,GAAG,EAAEZ,IAAI,CAACY,GAAG,CAAC,CAAC;IACnC;EACJ,CAAC,CAAC;;EAEF;EACAU,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEd,MAAM,CAACe,WAAW,CAACjB,QAAQ,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC;EAErF,MAAMlC,OAAO,GAAGR,SAAS,CAACC,GAAG,CAAC;EAC9B,MAAMQ,OAAO,GAAGd,aAAa,CAAC,qBAAqB,CAAC;EAEpD,IAAI;IACA,OAAO,MAAMN,KAAK,CAAC8C,KAAK,CAAC3B,OAAO,EAAEgB,QAAQ,EAAE;MAAEf;IAAQ,CAAC,CAAC;EAC5D,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAiC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;IACZ,MAAM/B,YAAY,GAAG,EAAA4B,gBAAA,GAAAjC,KAAK,CAACM,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsB1B,OAAO,KAAIR,KAAK,CAACQ,OAAO;IACnE,MAAMC,aAAa,GAAG,IAAIC,KAAK,CAAC,yBAAyBL,YAAY,EAAE,CAAC;IACxEI,aAAa,CAACE,aAAa,GAAGX,KAAK;IACnCS,aAAa,CAACG,MAAM,IAAAuB,gBAAA,GAAGnC,KAAK,CAACM,QAAQ,cAAA6B,gBAAA,uBAAdA,gBAAA,CAAgBvB,MAAM;IAC7CH,aAAa,CAACF,IAAI,IAAA6B,gBAAA,GAAGpC,KAAK,CAACM,QAAQ,cAAA8B,gBAAA,uBAAdA,gBAAA,CAAgB7B,IAAI;IACzC,MAAME,aAAa;EACvB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAe4B,GAAGA,CAAC9C,GAAG,EAAE;EAC3B,MAAMO,OAAO,GAAGR,SAAS,CAACC,GAAG,CAAC;EAC9B,MAAMQ,OAAO,GAAGd,aAAa,CAAC,CAAC;EAE/B,IAAI;IACA,OAAO,MAAMN,KAAK,CAAC2D,MAAM,CAACxC,OAAO,EAAE;MAAEC;IAAQ,CAAC,CAAC;EACnD,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAuC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA;IACZ,MAAMrC,YAAY,GAAG,EAAAkC,gBAAA,GAAAvC,KAAK,CAACM,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBhC,OAAO,KAAIR,KAAK,CAACQ,OAAO;IACnE,MAAMC,aAAa,GAAG,IAAIC,KAAK,CAAC,0BAA0BL,YAAY,EAAE,CAAC;IACzEI,aAAa,CAACE,aAAa,GAAGX,KAAK;IACnCS,aAAa,CAACG,MAAM,IAAA6B,gBAAA,GAAGzC,KAAK,CAACM,QAAQ,cAAAmC,gBAAA,uBAAdA,gBAAA,CAAgB7B,MAAM;IAC7CH,aAAa,CAACF,IAAI,IAAAmC,iBAAA,GAAG1C,KAAK,CAACM,QAAQ,cAAAoC,iBAAA,uBAAdA,iBAAA,CAAgBnC,IAAI;IACzC,MAAME,aAAa;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}