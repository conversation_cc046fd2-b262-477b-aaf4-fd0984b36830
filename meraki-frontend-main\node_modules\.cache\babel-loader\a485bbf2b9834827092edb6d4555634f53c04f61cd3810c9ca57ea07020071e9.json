{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\User\\\\components\\\\Form\\\\BasicInformation.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Button, Card, FormControl, Grid, InputBase, MenuItem, Typography, useTheme } from \"@mui/material\";\nimport { Autocomplete } from \"@mui/lab\";\nimport COUNTRIES from \"constants/countries\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { DepartmentSelector, DesignationSelector, GeneralSelector } from \"selectors\";\nimport { DepartmentActions, DesignationActions, GeneralActions, UserActions } from \"slices/actions\";\nimport { useFormik } from \"formik\";\nimport Input from \"components/Input\";\nimport SelectField from \"components/SelectField\";\nimport { toast } from \"react-toastify\";\nimport PropTypes from \"prop-types\";\nimport Can from \"../../../../utils/can\";\nimport { actions, features } from \"../../../../constants/permission\";\nimport { SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from \"../../../../constants/workSchedule\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nBasicInformation.propTypes = {\n  user: PropTypes.object,\n  form: PropTypes.object\n};\nexport default function BasicInformation(props) {\n  _s();\n  var _user$name, _user$country, _user$city, _user$address, _user$department$_id, _user$department, _user$designation$_id, _user$designation, _user$workHours, _user$workSchedule, _formik$values$workSc, _formik$values$workSc2, _formik$values$workSc3, _formik$values$workSc4, _formik$values$workSc5, _formik$values$workSc6;\n  const {\n    user,\n    form\n  } = props;\n  const dispatch = useDispatch();\n  const theme = useTheme();\n  const departments = useSelector(DepartmentSelector.getDepartments());\n  const designations = useSelector(DesignationSelector.getDesignations());\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  const [hoursFormat, setHoursFormat] = useState(\"decimal\"); // \"decimal\" or \"hhmm\"\n\n  const countries = COUNTRIES.map(item => ({\n    id: item.id,\n    name: item.name,\n    phoneCode: item.phoneCode,\n    flag: item.flag\n  }));\n  useEffect(() => {\n    dispatch(DepartmentActions.getDepartments());\n    dispatch(DesignationActions.getDesignations());\n  }, []);\n  useEffect(() => {\n    if (success) {\n      var _success$message;\n      toast.success(`${(_success$message = success === null || success === void 0 ? void 0 : success.message) !== null && _success$message !== void 0 ? _success$message : \"Success\"}`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true\n      });\n      dispatch(GeneralActions.removeSuccess(UserActions.updateUser.type));\n    }\n  }, [success]);\n\n  // Helper function to convert between decimal and HH:MM formats\n  const convertWorkHours = (value, toFormat) => {\n    if (!value) {\n      return \"\";\n    }\n    if (toFormat === \"decimal\") {\n      // Convert from HH:MM to decimal\n      if (value.includes(\":\")) {\n        const [hours, minutes] = value.split(\":\");\n        return Number(hours) + Number(minutes) / 60;\n      }\n      return value;\n    } else {\n      // Convert from decimal to HH:MM\n      const hours = Math.floor(Number(value));\n      const minutes = Math.round((Number(value) - hours) * 60);\n      return `${hours}:${minutes.toString().padStart(2, '0')}`;\n    }\n  };\n  const formik = useFormik({\n    initialValues: {\n      name: (_user$name = user === null || user === void 0 ? void 0 : user.name) !== null && _user$name !== void 0 ? _user$name : \"\",\n      phoneCode: '',\n      phoneNumber: \"\",\n      country: (_user$country = user === null || user === void 0 ? void 0 : user.country) !== null && _user$country !== void 0 ? _user$country : \"\",\n      city: (_user$city = user === null || user === void 0 ? void 0 : user.city) !== null && _user$city !== void 0 ? _user$city : \"\",\n      address: (_user$address = user === null || user === void 0 ? void 0 : user.address) !== null && _user$address !== void 0 ? _user$address : \"\",\n      department: (_user$department$_id = user === null || user === void 0 ? void 0 : (_user$department = user.department) === null || _user$department === void 0 ? void 0 : _user$department._id) !== null && _user$department$_id !== void 0 ? _user$department$_id : \"\",\n      designation: (_user$designation$_id = user === null || user === void 0 ? void 0 : (_user$designation = user.designation) === null || _user$designation === void 0 ? void 0 : _user$designation._id) !== null && _user$designation$_id !== void 0 ? _user$designation$_id : \"\",\n      workHours: (_user$workHours = user === null || user === void 0 ? void 0 : user.workHours) !== null && _user$workHours !== void 0 ? _user$workHours : \"8.5\",\n      // Default to 8.5 hours or use stored value\n      workSchedule: (_user$workSchedule = user === null || user === void 0 ? void 0 : user.workSchedule) !== null && _user$workSchedule !== void 0 ? _user$workSchedule : DEFAULT_WORK_SCHEDULE\n    },\n    enableReinitialize: true,\n    validateOnChange: true,\n    onSubmit: values => {\n      handleSubmit(values);\n    }\n  });\n  useEffect(() => {\n    var _formik$values$countr;\n    const code = (_formik$values$countr = formik.values.country) === null || _formik$values$countr === void 0 ? void 0 : _formik$values$countr.phoneCode;\n    const phone = formik.values.phone;\n    formik.setFieldValue('phoneCode', code !== null && code !== void 0 ? code : '');\n    formik.setFieldValue('phone', phone);\n  }, [formik.values.country]);\n  useEffect(() => {\n    const phone = user.phone;\n    const country = countries.find(e => e.name === user.country);\n    if (country) {\n      formik.setFieldValue('country', country);\n    }\n    if (phone && country) {\n      var _code$length;\n      const code = country.phoneCode;\n      formik.setFieldValue('phoneCode', code !== null && code !== void 0 ? code : '');\n      formik.setFieldValue('phoneNumber', phone.substring((_code$length = code.length) !== null && _code$length !== void 0 ? _code$length : 0));\n    }\n  }, [user]);\n  const handleSubmit = values => {\n    // Convert work hours to decimal format for storage\n    const workHoursDecimal = hoursFormat === \"decimal\" ? values.workHours : convertWorkHours(values.workHours, \"decimal\");\n\n    // Ensure workHours is a number, not a string\n    const workHoursNumber = parseFloat(workHoursDecimal);\n\n    // Log the value for debugging\n    console.log(\"Work hours being saved:\", workHoursNumber, typeof workHoursNumber);\n    console.log(\"Work schedule being saved:\", values.workSchedule);\n    const params = {\n      id: user._id,\n      ...values,\n      ...form,\n      phone: values.phoneCode + values.phoneNumber,\n      workHours: workHoursNumber,\n      workSchedule: values.workSchedule\n    };\n    dispatch(UserActions.updateUser(params));\n  };\n  const handleWorkHoursChange = e => {\n    const {\n      value\n    } = e.target;\n    formik.setFieldValue('workHours', value);\n  };\n  const toggleHoursFormat = () => {\n    const newFormat = hoursFormat === \"decimal\" ? \"hhmm\" : \"decimal\";\n    const convertedValue = convertWorkHours(formik.values.workHours, newFormat);\n    setHoursFormat(newFormat);\n    formik.setFieldValue('workHours', convertedValue);\n  };\n  const handleWorkScheduleChange = (field, value) => {\n    formik.setFieldValue(`workSchedule.${field}`, value);\n  };\n\n  // Helper function to format date for input field\n  const formatDateForInput = dateValue => {\n    if (!dateValue) {\n      return new Date().toISOString().split('T')[0]; // Current date\n    }\n    if (typeof dateValue === 'string') {\n      return dateValue.split('T')[0];\n    }\n    return new Date(dateValue).toISOString().split('T')[0];\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        mb: 4\n      },\n      children: \"Basic Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: formik.handleSubmit,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Full Name\",\n            name: \"name\",\n            value: formik.values.name,\n            onChange: formik.handleChange,\n            error: formik.touched.name && Boolean(formik.errors.name),\n            helperText: formik.touched.name && formik.errors.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Country\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n              disablePortal: true,\n              name: \"country\",\n              options: countries,\n              value: formik.values.country,\n              onChange: (e, val) => {\n                formik.setFieldValue('country', val);\n              },\n              error: formik.touched.country && Boolean(formik.errors.country),\n              helperText: formik.touched.country && formik.errors.country,\n              getOptionLabel: option => {\n                var _option$name;\n                return (_option$name = option.name) !== null && _option$name !== void 0 ? _option$name : '';\n              },\n              renderOption: (props, option) => /*#__PURE__*/_jsxDEV(Box, {\n                component: \"li\",\n                sx: {\n                  '& > img': {\n                    mr: 2,\n                    flexShrink: 0\n                  }\n                },\n                ...props,\n                children: [option.flag, \" \", option.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 37\n              }, this),\n              renderInput: params => /*#__PURE__*/_jsxDEV(InputBase, {\n                ...params.InputProps,\n                ...params\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 58\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: 80\n                },\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  sx: {\n                    textAlign: 'center',\n                    '& .Mui-disabled': {\n                      fillColor: theme.palette.common.black\n                    }\n                  },\n                  autoComplete: \"new-password\",\n                  name: \"phoneCode\",\n                  startAdornment: \"+\",\n                  type: \"number\",\n                  value: formik.values.phoneCode,\n                  onChange: formik.handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                name: \"phoneNumber\",\n                value: formik.values.phoneNumber,\n                onChange: formik.handleChange,\n                error: formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber),\n                helperText: formik.touched.phoneNumber && formik.errors.phoneNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"City\",\n            name: \"city\",\n            value: formik.values.city,\n            onChange: formik.handleChange,\n            error: formik.touched.city && Boolean(formik.errors.city),\n            helperText: formik.touched.city && formik.errors.city\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Address\",\n            name: \"address\",\n            value: formik.values.address,\n            onChange: formik.handleChange,\n            error: formik.touched.address && Boolean(formik.errors.address),\n            helperText: formik.touched.address && formik.errors.address\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Department\",\n            name: \"department\",\n            value: formik.values.department,\n            onChange: formik.handleChange,\n            error: formik.touched.department && Boolean(formik.errors.department),\n            helperText: formik.touched.department && formik.errors.department,\n            children: departments.map((item, index) => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: item._id,\n              children: item.name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Designation\",\n            name: \"designation\",\n            value: formik.values.designation,\n            onChange: formik.handleChange,\n            error: formik.touched.designation && Boolean(formik.errors.designation),\n            helperText: formik.touched.designation && formik.errors.designation,\n            children: designations.map((item, index) => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: item._id,\n              children: item.name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Daily Work Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1.5,\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                label: \"\",\n                name: \"workHours\",\n                value: formik.values.workHours,\n                onChange: handleWorkHoursChange,\n                placeholder: hoursFormat === \"decimal\" ? \"e.g. 8.5\" : \"e.g. 8:30\",\n                error: formik.touched.workHours && Boolean(formik.errors.workHours),\n                helperText: formik.touched.workHours && formik.errors.workHours\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                variant: \"outlined\",\n                onClick: toggleHoursFormat,\n                children: hoursFormat === \"decimal\" ? \"Use HH:MM\" : \"Use Decimal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                mt: 1,\n                color: 'text.secondary'\n              },\n              children: hoursFormat === \"decimal\" ? \"Enter as decimal (e.g., 8.5 for 8 hours 30 minutes)\" : \"Enter as hours:minutes (e.g., 8:30)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                mt: 1,\n                display: 'block',\n                color: 'info.main'\n              },\n              children: \"Note: This value determines your daily work requirement. Working less than half of this time will be marked as a half-day. Working more will count as overtime.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Schedule Template\",\n            name: \"workSchedule.scheduleTemplate\",\n            value: ((_formik$values$workSc = formik.values.workSchedule) === null || _formik$values$workSc === void 0 ? void 0 : _formik$values$workSc.scheduleTemplate) || 'day_shift',\n            onChange: e => handleWorkScheduleChange('scheduleTemplate', e.target.value),\n            children: SCHEDULE_TEMPLATES.map(template => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: template.value,\n              children: template.label\n            }, template.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Shift Start\",\n            name: \"workSchedule.shiftStart\",\n            type: \"date\",\n            value: (_formik$values$workSc2 = formik.values.workSchedule) !== null && _formik$values$workSc2 !== void 0 && _formik$values$workSc2.shiftStart ? typeof formik.values.workSchedule.shiftStart === 'string' ? formik.values.workSchedule.shiftStart.split('T')[0] : new Date(formik.values.workSchedule.shiftStart).toISOString().split('T')[0] : '2025-06-11',\n            onChange: e => handleWorkScheduleChange('shiftStart', e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Shift End\",\n            name: \"workSchedule.shiftEnd\",\n            type: \"date\",\n            value: (_formik$values$workSc3 = formik.values.workSchedule) !== null && _formik$values$workSc3 !== void 0 && _formik$values$workSc3.shiftEnd ? typeof formik.values.workSchedule.shiftEnd === 'string' ? formik.values.workSchedule.shiftEnd.split('T')[0] : new Date(formik.values.workSchedule.shiftEnd).toISOString().split('T')[0] : '2025-06-11',\n            onChange: e => handleWorkScheduleChange('shiftEnd', e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Start Time\",\n            name: \"workSchedule.startTime\",\n            value: ((_formik$values$workSc4 = formik.values.workSchedule) === null || _formik$values$workSc4 === void 0 ? void 0 : _formik$values$workSc4.startTime) || '09:00',\n            onChange: e => handleWorkScheduleChange('startTime', e.target.value),\n            children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"End Time\",\n            name: \"workSchedule.endTime\",\n            value: ((_formik$values$workSc5 = formik.values.workSchedule) === null || _formik$values$workSc5 === void 0 ? void 0 : _formik$values$workSc5.endTime) || '17:00',\n            onChange: e => handleWorkScheduleChange('endTime', e.target.value),\n            children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: option.value,\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Minimum Hours\",\n            name: \"workSchedule.minimumHours\",\n            type: \"number\",\n            step: \"0.5\",\n            value: ((_formik$values$workSc6 = formik.values.workSchedule) === null || _formik$values$workSc6 === void 0 ? void 0 : _formik$values$workSc6.minimumHours) || 8.0,\n            onChange: e => handleWorkScheduleChange('minimumHours', parseFloat(e.target.value))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 21\n        }, this), Can(actions.readAll, features.user) && /*#__PURE__*/_jsxDEV(Grid, {\n          sx: {\n            mt: 3\n          },\n          item: true,\n          container: true,\n          justifyContent: \"flex-end\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            color: \"primary\",\n            variant: \"contained\",\n            children: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 9\n  }, this);\n}\n_s(BasicInformation, \"nVcy5cbiLXR/E+141lGOoqWoQyE=\", false, function () {\n  return [useDispatch, useTheme, useSelector, useSelector, useSelector, useFormik];\n});\n_c = BasicInformation;\nvar _c;\n$RefreshReg$(_c, \"BasicInformation\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "<PERSON><PERSON>", "Card", "FormControl", "Grid", "InputBase", "MenuItem", "Typography", "useTheme", "Autocomplete", "COUNTRIES", "useDispatch", "useSelector", "DepartmentSelector", "DesignationSelector", "GeneralSelector", "DepartmentActions", "DesignationActions", "GeneralActions", "UserActions", "useFormik", "Input", "SelectField", "toast", "PropTypes", "Can", "actions", "features", "SCHEDULE_TEMPLATES", "DEFAULT_WORK_SCHEDULE", "TIME_OPTIONS", "jsxDEV", "_jsxDEV", "BasicInformation", "propTypes", "user", "object", "form", "props", "_s", "_user$name", "_user$country", "_user$city", "_user$address", "_user$department$_id", "_user$department", "_user$designation$_id", "_user$designation", "_user$workHours", "_user$workSchedule", "_formik$values$workSc", "_formik$values$workSc2", "_formik$values$workSc3", "_formik$values$workSc4", "_formik$values$workSc5", "_formik$values$workSc6", "dispatch", "theme", "departments", "getDepartments", "designations", "getDesignations", "success", "updateUser", "type", "hoursFormat", "setHoursFormat", "countries", "map", "item", "id", "name", "phoneCode", "flag", "_success$message", "message", "position", "autoClose", "closeOnClick", "removeSuccess", "convertWorkHours", "value", "toFormat", "includes", "hours", "minutes", "split", "Number", "Math", "floor", "round", "toString", "padStart", "formik", "initialValues", "phoneNumber", "country", "city", "address", "department", "_id", "designation", "workHours", "workSchedule", "enableReinitialize", "validateOnChange", "onSubmit", "values", "handleSubmit", "_formik$values$countr", "code", "phone", "setFieldValue", "find", "e", "_code$length", "substring", "length", "workHoursDecimal", "workHoursNumber", "parseFloat", "console", "log", "params", "handleWorkHoursChange", "target", "toggleHoursFormat", "newFormat", "convertedValue", "handleWorkScheduleChange", "field", "formatDateForInput", "dateValue", "Date", "toISOString", "children", "variant", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "lg", "xs", "label", "onChange", "handleChange", "error", "touched", "Boolean", "errors", "helperText", "fullWidth", "disable<PERSON><PERSON><PERSON>", "options", "val", "getOptionLabel", "option", "_option$name", "renderOption", "component", "mr", "flexShrink", "renderInput", "InputProps", "display", "gap", "width", "textAlign", "fillColor", "palette", "common", "black", "autoComplete", "startAdornment", "index", "alignItems", "placeholder", "size", "onClick", "mt", "color", "scheduleTemplate", "template", "shiftStart", "shiftEnd", "startTime", "endTime", "step", "minimumHours", "readAll", "justifyContent", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/User/components/Form/BasicInformation.js"], "sourcesContent": ["import React, {useEffect, useState} from \"react\";\r\nimport {\r\n    Box,\r\n    Button,\r\n    Card,\r\n    FormControl,\r\n    Grid,\r\n    InputBase,\r\n    MenuItem,\r\n    Typography,\r\n    useTheme\r\n} from \"@mui/material\";\r\nimport {Autocomplete} from \"@mui/lab\";\r\nimport COUNTRIES from \"constants/countries\";\r\nimport {useDispatch, useSelector} from \"react-redux\";\r\nimport {DepartmentSelector, DesignationSelector, GeneralSelector} from \"selectors\";\r\nimport {DepartmentActions, DesignationActions, GeneralActions, UserActions} from \"slices/actions\";\r\nimport {useFormik} from \"formik\";\r\nimport Input from \"components/Input\";\r\nimport SelectField from \"components/SelectField\";\r\nimport {toast} from \"react-toastify\";\r\nimport PropTypes from \"prop-types\";\r\nimport Can from \"../../../../utils/can\";\r\nimport {actions, features} from \"../../../../constants/permission\";\r\nimport {SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS} from \"../../../../constants/workSchedule\";\r\n\r\nBasicInformation.propTypes = {\r\n    user: PropTypes.object,\r\n    form: PropTypes.object\r\n};\r\n\r\nexport default function BasicInformation(props) {\r\n    const { user, form } = props;\r\n    const dispatch = useDispatch();\r\n    const theme = useTheme();\r\n    const departments = useSelector(DepartmentSelector.getDepartments());\r\n    const designations = useSelector(DesignationSelector.getDesignations());\r\n    const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\r\n\r\n    const [hoursFormat, setHoursFormat] = useState(\"decimal\"); // \"decimal\" or \"hhmm\"\r\n\r\n    const countries = COUNTRIES.map(item => ({\r\n        id: item.id,\r\n        name: item.name,\r\n        phoneCode: item.phoneCode,\r\n        flag: item.flag\r\n    }));\r\n\r\n    useEffect(() => {\r\n        dispatch(DepartmentActions.getDepartments());\r\n        dispatch(DesignationActions.getDesignations());\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (success) {\r\n            toast.success(`${success?.message ?? \"Success\"}`, {\r\n                    position: \"top-right\",\r\n                    autoClose: 3000,\r\n                    closeOnClick: true,\r\n                });\r\n\r\n            dispatch(GeneralActions.removeSuccess(UserActions.updateUser.type));\r\n        }\r\n    }, [success]);\r\n\r\n    // Helper function to convert between decimal and HH:MM formats\r\n    const convertWorkHours = (value, toFormat) => {\r\n        if (!value) {\r\n            return \"\";\r\n        }\r\n\r\n        if (toFormat === \"decimal\") {\r\n            // Convert from HH:MM to decimal\r\n            if (value.includes(\":\")) {\r\n                const [hours, minutes] = value.split(\":\");\r\n                return Number(hours) + (Number(minutes) / 60);\r\n            }\r\n            return value;\r\n        } else {\r\n            // Convert from decimal to HH:MM\r\n            const hours = Math.floor(Number(value));\r\n            const minutes = Math.round((Number(value) - hours) * 60);\r\n            return `${hours}:${minutes.toString().padStart(2, '0')}`;\r\n        }\r\n    };\r\n\r\n    const formik = useFormik({\r\n        initialValues: {\r\n            name: user?.name ?? \"\",\r\n            phoneCode: '',\r\n            phoneNumber: \"\",\r\n            country: user?.country ?? \"\",\r\n            city: user?.city ?? \"\",\r\n            address: user?.address ?? \"\",\r\n            department: user?.department?._id ?? \"\",\r\n            designation: user?.designation?._id ?? \"\",\r\n            workHours: user?.workHours ?? \"8.5\", // Default to 8.5 hours or use stored value\r\n            workSchedule: user?.workSchedule ?? DEFAULT_WORK_SCHEDULE,\r\n        },\r\n        enableReinitialize: true,\r\n        validateOnChange: true,\r\n        onSubmit: (values) => {\r\n            handleSubmit(values);\r\n        }\r\n    });\r\n\r\n    useEffect(() => {\r\n        const code = formik.values.country?.phoneCode;\r\n        const phone = formik.values.phone;\r\n\r\n        formik.setFieldValue('phoneCode', code ?? '');\r\n        formik.setFieldValue('phone', phone);\r\n    }, [formik.values.country]);\r\n\r\n    useEffect(() => {\r\n        const phone = user.phone;\r\n        const country = countries.find(e => e.name === user.country);\r\n\r\n        if (country) {\r\n            formik.setFieldValue('country', country);\r\n        }\r\n\r\n        if (phone && country) {\r\n            const code = country.phoneCode;\r\n\r\n            formik.setFieldValue('phoneCode', code ?? '');\r\n            formik.setFieldValue('phoneNumber', phone.substring(code.length ?? 0));\r\n        }\r\n    }, [user]);\r\n\r\n    const handleSubmit = (values) => {\r\n        // Convert work hours to decimal format for storage\r\n        const workHoursDecimal = hoursFormat === \"decimal\" ? values.workHours : convertWorkHours(values.workHours, \"decimal\");\r\n\r\n        // Ensure workHours is a number, not a string\r\n        const workHoursNumber = parseFloat(workHoursDecimal);\r\n\r\n        // Log the value for debugging\r\n        console.log(\"Work hours being saved:\", workHoursNumber, typeof workHoursNumber);\r\n        console.log(\"Work schedule being saved:\", values.workSchedule);\r\n\r\n        const params = {\r\n            id: user._id,\r\n            ...values,\r\n            ...form,\r\n            phone: values.phoneCode + values.phoneNumber,\r\n            workHours: workHoursNumber,\r\n            workSchedule: values.workSchedule\r\n        };\r\n\r\n        dispatch(UserActions.updateUser(params));\r\n    }\r\n\r\n    const handleWorkHoursChange = (e) => {\r\n        const { value } = e.target;\r\n        formik.setFieldValue('workHours', value);\r\n    };\r\n\r\n    const toggleHoursFormat = () => {\r\n        const newFormat = hoursFormat === \"decimal\" ? \"hhmm\" : \"decimal\";\r\n        const convertedValue = convertWorkHours(formik.values.workHours, newFormat);\r\n        setHoursFormat(newFormat);\r\n        formik.setFieldValue('workHours', convertedValue);\r\n    };\r\n\r\n    const handleWorkScheduleChange = (field, value) => {\r\n        formik.setFieldValue(`workSchedule.${field}`, value);\r\n    };\r\n\r\n    // Helper function to format date for input field\r\n    const formatDateForInput = (dateValue) => {\r\n        if (!dateValue) {\r\n            return new Date().toISOString().split('T')[0]; // Current date\r\n        }\r\n\r\n        if (typeof dateValue === 'string') {\r\n            return dateValue.split('T')[0];\r\n        }\r\n\r\n        return new Date(dateValue).toISOString().split('T')[0];\r\n    };\r\n\r\n    return (\r\n        <Card>\r\n            <Typography variant='h5' sx={{ mb: 4 }}>Basic Information</Typography>\r\n            <form onSubmit={formik.handleSubmit}>\r\n                <Grid container spacing={2}>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n\r\n                            label=\"Full Name\"\r\n                            name='name'\r\n                            value={formik.values.name}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.name && Boolean(formik.errors.name)}\r\n                            helperText={formik.touched.name && formik.errors.name}/>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <FormControl fullWidth>\r\n                            <Typography variant='caption'>Country</Typography>\r\n                            <Autocomplete\r\n                                disablePortal\r\n\r\n                                name='country'\r\n                                options={countries}\r\n                                value={formik.values.country}\r\n                                onChange={(e, val) => {\r\n                                    formik.setFieldValue('country', val);\r\n                                }}\r\n                                error={formik.touched.country && Boolean(formik.errors.country)}\r\n                                helperText={formik.touched.country && formik.errors.country}\r\n                                getOptionLabel={(option) => option.name ?? ''}\r\n                                renderOption={(props, option) => (\r\n                                    <Box component=\"li\" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>\r\n                                        {option.flag} {option.name}\r\n                                    </Box>\r\n                                )}\r\n                                renderInput={(params) => <InputBase {...params.InputProps} {...params} />}\r\n                            />\r\n                        </FormControl>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <FormControl fullWidth>\r\n                            <Typography variant='caption'>Phone Number</Typography>\r\n                            <Box sx={{\r\n                                display: 'flex',\r\n                                gap: 1.5\r\n                            }}>\r\n                                <Box sx={{ width: 80 }}>\r\n                                    <Input\r\n                                        sx={{\r\n                                            textAlign: 'center',\r\n                                            '& .Mui-disabled': {\r\n                                                fillColor: theme.palette.common.black\r\n                                            }\r\n                                        }}\r\n\r\n                                        autoComplete='new-password'\r\n                                        name='phoneCode'\r\n                                        startAdornment='+'\r\n                                        type='number'\r\n                                        value={formik.values.phoneCode}\r\n                                        onChange={formik.handleChange}/>\r\n                                </Box>\r\n                                <Input\r\n\r\n                                    name='phoneNumber'\r\n                                    value={formik.values.phoneNumber}\r\n                                    onChange={formik.handleChange}\r\n                                    error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}\r\n                                    helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}/>\r\n                            </Box>\r\n                        </FormControl>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"City\"\r\n                            name='city'\r\n                            value={formik.values.city}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.city && Boolean(formik.errors.city)}\r\n                            helperText={formik.touched.city && formik.errors.city}\r\n                            />\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"Address\"\r\n                            name='address'\r\n                            value={formik.values.address}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.address && Boolean(formik.errors.address)}\r\n                            helperText={formik.touched.address && formik.errors.address}\r\n                            />\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Department\"\r\n                            name='department'\r\n                            value={formik.values.department}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.department && Boolean(formik.errors.department)}\r\n                            helperText={formik.touched.department && formik.errors.department}\r\n                            >\r\n                            {departments.map((item, index) => (\r\n                                <MenuItem key={index} value={item._id}>\r\n                                    {item.name}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Designation\"\r\n                            name='designation'\r\n                            value={formik.values.designation}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.designation && Boolean(formik.errors.designation)}\r\n                            helperText={formik.touched.designation && formik.errors.designation}\r\n                            >\r\n                            {designations.map((item, index) => (\r\n                                <MenuItem key={index} value={item._id}>\r\n                                    {item.name}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n\r\n                    {/* New Work Hours Field */}\r\n                    <Grid item lg={6} xs={12}>\r\n                        <FormControl fullWidth>\r\n                            <Typography variant='caption'>Daily Work Hours</Typography>\r\n                            <Box sx={{\r\n                                display: 'flex',\r\n                                gap: 1.5,\r\n                                alignItems: 'center'\r\n                            }}>\r\n                                <Input\r\n\r\n                                    label=\"\"\r\n                                    name='workHours'\r\n                                    value={formik.values.workHours}\r\n                                    onChange={handleWorkHoursChange}\r\n                                    placeholder={hoursFormat === \"decimal\" ? \"e.g. 8.5\" : \"e.g. 8:30\"}\r\n                                    error={formik.touched.workHours && Boolean(formik.errors.workHours)}\r\n                                    helperText={formik.touched.workHours && formik.errors.workHours}/>\r\n\r\n                                <Button\r\n                                    size=\"small\"\r\n                                    variant=\"outlined\"\r\n                                    onClick={toggleHoursFormat}\r\n                                    >\r\n                                    {hoursFormat === \"decimal\" ? \"Use HH:MM\" : \"Use Decimal\"}\r\n                                </Button>\r\n                            </Box>\r\n                            <Typography variant='caption' sx={{ mt: 1, color: 'text.secondary' }}>\r\n                                {hoursFormat === \"decimal\" ? \"Enter as decimal (e.g., 8.5 for 8 hours 30 minutes)\" : \"Enter as hours:minutes (e.g., 8:30)\"}\r\n                            </Typography>\r\n                            <Typography variant='caption' sx={{ mt: 1, display: 'block', color: 'info.main' }}>\r\n                                Note: This value determines your daily work requirement. Working less than half of this time will be marked as a half-day. Working more will count as overtime.\r\n                            </Typography>\r\n                        </FormControl>\r\n                    </Grid>\r\n\r\n                    {/* Work Schedule Fields */}\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Schedule Template\"\r\n                            name='workSchedule.scheduleTemplate'\r\n                            value={formik.values.workSchedule?.scheduleTemplate || 'day_shift'}\r\n                            onChange={(e) => handleWorkScheduleChange('scheduleTemplate', e.target.value)}\r\n                            >\r\n                            {SCHEDULE_TEMPLATES.map((template) => (\r\n                                <MenuItem key={template.value} value={template.value}>\r\n                                    {template.label}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"Shift Start\"\r\n                            name='workSchedule.shiftStart'\r\n                            type=\"date\"\r\n                            value={\r\n                                formik.values.workSchedule?.shiftStart ? (typeof formik.values.workSchedule.shiftStart === 'string' ? formik.values.workSchedule.shiftStart.split('T')[0] : new Date(formik.values.workSchedule.shiftStart).toISOString().split('T')[0]) : '2025-06-11'\r\n                            }\r\n                            onChange={(e) => handleWorkScheduleChange('shiftStart', e.target.value)}\r\n                            />\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"Shift End\"\r\n                            name='workSchedule.shiftEnd'\r\n                            type=\"date\"\r\n                            value={\r\n                                formik.values.workSchedule?.shiftEnd ? (typeof formik.values.workSchedule.shiftEnd === 'string' ? formik.values.workSchedule.shiftEnd.split('T')[0] : new Date(formik.values.workSchedule.shiftEnd).toISOString().split('T')[0]) : '2025-06-11'\r\n                            }\r\n                            onChange={(e) => handleWorkScheduleChange('shiftEnd', e.target.value)}\r\n                            />\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Start Time\"\r\n                            name='workSchedule.startTime'\r\n                            value={formik.values.workSchedule?.startTime || '09:00'}\r\n                            onChange={(e) => handleWorkScheduleChange('startTime', e.target.value)}\r\n                            >\r\n                            {TIME_OPTIONS.map((option) => (\r\n                                <MenuItem key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"End Time\"\r\n                            name='workSchedule.endTime'\r\n                            value={formik.values.workSchedule?.endTime || '17:00'}\r\n                            onChange={(e) => handleWorkScheduleChange('endTime', e.target.value)}\r\n                            >\r\n                            {TIME_OPTIONS.map((option) => (\r\n                                <MenuItem key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"Minimum Hours\"\r\n                            name='workSchedule.minimumHours'\r\n                            type=\"number\"\r\n                            step=\"0.5\"\r\n                            value={formik.values.workSchedule?.minimumHours || 8.0}\r\n                            onChange={(e) => handleWorkScheduleChange('minimumHours', parseFloat(e.target.value))}\r\n                            />\r\n                    </Grid>\r\n\r\n                    {Can(actions.readAll, features.user) && (\r\n                        <Grid sx={{ mt: 3 }} item container justifyContent=\"flex-end\">\r\n                            <Button\r\n                                type=\"submit\"\r\n                                color=\"primary\"\r\n                                variant=\"contained\">\r\n                                Submit\r\n                            </Button>\r\n                        </Grid>\r\n                    )}\r\n                </Grid>\r\n            </form>\r\n        </Card>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,SAAS,EAAEC,QAAQ,QAAO,OAAO;AAChD,SACIC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,QAAQ,QACL,eAAe;AACtB,SAAQC,YAAY,QAAO,UAAU;AACrC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAAQC,WAAW,EAAEC,WAAW,QAAO,aAAa;AACpD,SAAQC,kBAAkB,EAAEC,mBAAmB,EAAEC,eAAe,QAAO,WAAW;AAClF,SAAQC,iBAAiB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,QAAO,gBAAgB;AACjG,SAAQC,SAAS,QAAO,QAAQ;AAChC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAAQC,KAAK,QAAO,gBAAgB;AACpC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAAQC,OAAO,EAAEC,QAAQ,QAAO,kCAAkC;AAClE,SAAQC,kBAAkB,EAAEC,qBAAqB,EAAEC,YAAY,QAAO,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3GC,gBAAgB,CAACC,SAAS,GAAG;EACzBC,IAAI,EAAEX,SAAS,CAACY,MAAM;EACtBC,IAAI,EAAEb,SAAS,CAACY;AACpB,CAAC;AAED,eAAe,SAASH,gBAAgBA,CAACK,KAAK,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,eAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5C,MAAM;IAAEpB,IAAI;IAAEE;EAAK,CAAC,GAAGC,KAAK;EAC5B,MAAMkB,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM8C,KAAK,GAAGjD,QAAQ,CAAC,CAAC;EACxB,MAAMkD,WAAW,GAAG9C,WAAW,CAACC,kBAAkB,CAAC8C,cAAc,CAAC,CAAC,CAAC;EACpE,MAAMC,YAAY,GAAGhD,WAAW,CAACE,mBAAmB,CAAC+C,eAAe,CAAC,CAAC,CAAC;EACvE,MAAMC,OAAO,GAAGlD,WAAW,CAACG,eAAe,CAAC+C,OAAO,CAAC3C,WAAW,CAAC4C,UAAU,CAACC,IAAI,CAAC,CAAC;EAEjF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;EAE3D,MAAMoE,SAAS,GAAGzD,SAAS,CAAC0D,GAAG,CAACC,IAAI,KAAK;IACrCC,EAAE,EAAED,IAAI,CAACC,EAAE;IACXC,IAAI,EAAEF,IAAI,CAACE,IAAI;IACfC,SAAS,EAAEH,IAAI,CAACG,SAAS;IACzBC,IAAI,EAAEJ,IAAI,CAACI;EACf,CAAC,CAAC,CAAC;EAEH3E,SAAS,CAAC,MAAM;IACZ0D,QAAQ,CAACxC,iBAAiB,CAAC2C,cAAc,CAAC,CAAC,CAAC;IAC5CH,QAAQ,CAACvC,kBAAkB,CAAC4C,eAAe,CAAC,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EAEN/D,SAAS,CAAC,MAAM;IACZ,IAAIgE,OAAO,EAAE;MAAA,IAAAY,gBAAA;MACTnD,KAAK,CAACuC,OAAO,CAAC,IAAAY,gBAAA,GAAGZ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,OAAO,cAAAD,gBAAA,cAAAA,gBAAA,GAAI,SAAS,EAAE,EAAE;QAC1CE,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE;MAClB,CAAC,CAAC;MAENtB,QAAQ,CAACtC,cAAc,CAAC6D,aAAa,CAAC5D,WAAW,CAAC4C,UAAU,CAACC,IAAI,CAAC,CAAC;IACvE;EACJ,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMkB,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC1C,IAAI,CAACD,KAAK,EAAE;MACR,OAAO,EAAE;IACb;IAEA,IAAIC,QAAQ,KAAK,SAAS,EAAE;MACxB;MACA,IAAID,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;QACrB,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGJ,KAAK,CAACK,KAAK,CAAC,GAAG,CAAC;QACzC,OAAOC,MAAM,CAACH,KAAK,CAAC,GAAIG,MAAM,CAACF,OAAO,CAAC,GAAG,EAAG;MACjD;MACA,OAAOJ,KAAK;IAChB,CAAC,MAAM;MACH;MACA,MAAMG,KAAK,GAAGI,IAAI,CAACC,KAAK,CAACF,MAAM,CAACN,KAAK,CAAC,CAAC;MACvC,MAAMI,OAAO,GAAGG,IAAI,CAACE,KAAK,CAAC,CAACH,MAAM,CAACN,KAAK,CAAC,GAAGG,KAAK,IAAI,EAAE,CAAC;MACxD,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC5D;EACJ,CAAC;EAED,MAAMC,MAAM,GAAGzE,SAAS,CAAC;IACrB0E,aAAa,EAAE;MACXvB,IAAI,GAAA/B,UAAA,GAAEL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,cAAA/B,UAAA,cAAAA,UAAA,GAAI,EAAE;MACtBgC,SAAS,EAAE,EAAE;MACbuB,WAAW,EAAE,EAAE;MACfC,OAAO,GAAAvD,aAAA,GAAEN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,OAAO,cAAAvD,aAAA,cAAAA,aAAA,GAAI,EAAE;MAC5BwD,IAAI,GAAAvD,UAAA,GAAEP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,IAAI,cAAAvD,UAAA,cAAAA,UAAA,GAAI,EAAE;MACtBwD,OAAO,GAAAvD,aAAA,GAAER,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,OAAO,cAAAvD,aAAA,cAAAA,aAAA,GAAI,EAAE;MAC5BwD,UAAU,GAAAvD,oBAAA,GAAET,IAAI,aAAJA,IAAI,wBAAAU,gBAAA,GAAJV,IAAI,CAAEgE,UAAU,cAAAtD,gBAAA,uBAAhBA,gBAAA,CAAkBuD,GAAG,cAAAxD,oBAAA,cAAAA,oBAAA,GAAI,EAAE;MACvCyD,WAAW,GAAAvD,qBAAA,GAAEX,IAAI,aAAJA,IAAI,wBAAAY,iBAAA,GAAJZ,IAAI,CAAEkE,WAAW,cAAAtD,iBAAA,uBAAjBA,iBAAA,CAAmBqD,GAAG,cAAAtD,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MACzCwD,SAAS,GAAAtD,eAAA,GAAEb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,SAAS,cAAAtD,eAAA,cAAAA,eAAA,GAAI,KAAK;MAAE;MACrCuD,YAAY,GAAAtD,kBAAA,GAAEd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,YAAY,cAAAtD,kBAAA,cAAAA,kBAAA,GAAIpB;IACxC,CAAC;IACD2E,kBAAkB,EAAE,IAAI;IACxBC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAGC,MAAM,IAAK;MAClBC,YAAY,CAACD,MAAM,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF7G,SAAS,CAAC,MAAM;IAAA,IAAA+G,qBAAA;IACZ,MAAMC,IAAI,IAAAD,qBAAA,GAAGhB,MAAM,CAACc,MAAM,CAACX,OAAO,cAAAa,qBAAA,uBAArBA,qBAAA,CAAuBrC,SAAS;IAC7C,MAAMuC,KAAK,GAAGlB,MAAM,CAACc,MAAM,CAACI,KAAK;IAEjClB,MAAM,CAACmB,aAAa,CAAC,WAAW,EAAEF,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,CAAC;IAC7CjB,MAAM,CAACmB,aAAa,CAAC,OAAO,EAAED,KAAK,CAAC;EACxC,CAAC,EAAE,CAAClB,MAAM,CAACc,MAAM,CAACX,OAAO,CAAC,CAAC;EAE3BlG,SAAS,CAAC,MAAM;IACZ,MAAMiH,KAAK,GAAG5E,IAAI,CAAC4E,KAAK;IACxB,MAAMf,OAAO,GAAG7B,SAAS,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3C,IAAI,KAAKpC,IAAI,CAAC6D,OAAO,CAAC;IAE5D,IAAIA,OAAO,EAAE;MACTH,MAAM,CAACmB,aAAa,CAAC,SAAS,EAAEhB,OAAO,CAAC;IAC5C;IAEA,IAAIe,KAAK,IAAIf,OAAO,EAAE;MAAA,IAAAmB,YAAA;MAClB,MAAML,IAAI,GAAGd,OAAO,CAACxB,SAAS;MAE9BqB,MAAM,CAACmB,aAAa,CAAC,WAAW,EAAEF,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,CAAC;MAC7CjB,MAAM,CAACmB,aAAa,CAAC,aAAa,EAAED,KAAK,CAACK,SAAS,EAAAD,YAAA,GAACL,IAAI,CAACO,MAAM,cAAAF,YAAA,cAAAA,YAAA,GAAI,CAAC,CAAC,CAAC;IAC1E;EACJ,CAAC,EAAE,CAAChF,IAAI,CAAC,CAAC;EAEV,MAAMyE,YAAY,GAAID,MAAM,IAAK;IAC7B;IACA,MAAMW,gBAAgB,GAAGrD,WAAW,KAAK,SAAS,GAAG0C,MAAM,CAACL,SAAS,GAAGtB,gBAAgB,CAAC2B,MAAM,CAACL,SAAS,EAAE,SAAS,CAAC;;IAErH;IACA,MAAMiB,eAAe,GAAGC,UAAU,CAACF,gBAAgB,CAAC;;IAEpD;IACAG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,eAAe,EAAE,OAAOA,eAAe,CAAC;IAC/EE,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEf,MAAM,CAACJ,YAAY,CAAC;IAE9D,MAAMoB,MAAM,GAAG;MACXrD,EAAE,EAAEnC,IAAI,CAACiE,GAAG;MACZ,GAAGO,MAAM;MACT,GAAGtE,IAAI;MACP0E,KAAK,EAAEJ,MAAM,CAACnC,SAAS,GAAGmC,MAAM,CAACZ,WAAW;MAC5CO,SAAS,EAAEiB,eAAe;MAC1BhB,YAAY,EAAEI,MAAM,CAACJ;IACzB,CAAC;IAED/C,QAAQ,CAACrC,WAAW,CAAC4C,UAAU,CAAC4D,MAAM,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMC,qBAAqB,GAAIV,CAAC,IAAK;IACjC,MAAM;MAAEjC;IAAM,CAAC,GAAGiC,CAAC,CAACW,MAAM;IAC1BhC,MAAM,CAACmB,aAAa,CAAC,WAAW,EAAE/B,KAAK,CAAC;EAC5C,CAAC;EAED,MAAM6C,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG9D,WAAW,KAAK,SAAS,GAAG,MAAM,GAAG,SAAS;IAChE,MAAM+D,cAAc,GAAGhD,gBAAgB,CAACa,MAAM,CAACc,MAAM,CAACL,SAAS,EAAEyB,SAAS,CAAC;IAC3E7D,cAAc,CAAC6D,SAAS,CAAC;IACzBlC,MAAM,CAACmB,aAAa,CAAC,WAAW,EAAEgB,cAAc,CAAC;EACrD,CAAC;EAED,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEjD,KAAK,KAAK;IAC/CY,MAAM,CAACmB,aAAa,CAAC,gBAAgBkB,KAAK,EAAE,EAAEjD,KAAK,CAAC;EACxD,CAAC;;EAED;EACA,MAAMkD,kBAAkB,GAAIC,SAAS,IAAK;IACtC,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAChD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD;IAEA,IAAI,OAAO8C,SAAS,KAAK,QAAQ,EAAE;MAC/B,OAAOA,SAAS,CAAC9C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAClC;IAEA,OAAO,IAAI+C,IAAI,CAACD,SAAS,CAAC,CAACE,WAAW,CAAC,CAAC,CAAChD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1D,CAAC;EAED,oBACItD,OAAA,CAAC9B,IAAI;IAAAqI,QAAA,gBACDvG,OAAA,CAACzB,UAAU;MAACiI,OAAO,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EAAC;IAAiB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACtE9G,OAAA;MAAM0E,QAAQ,EAAEb,MAAM,CAACe,YAAa;MAAA2B,QAAA,eAChCvG,OAAA,CAAC5B,IAAI;QAAC2I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAT,QAAA,gBACvBvG,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACX,KAAK;YAEF8H,KAAK,EAAC,WAAW;YACjB5E,IAAI,EAAC,MAAM;YACXU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACpC,IAAK;YAC1B6E,QAAQ,EAAEvD,MAAM,CAACwD,YAAa;YAC9BC,KAAK,EAAEzD,MAAM,CAAC0D,OAAO,CAAChF,IAAI,IAAIiF,OAAO,CAAC3D,MAAM,CAAC4D,MAAM,CAAClF,IAAI,CAAE;YAC1DmF,UAAU,EAAE7D,MAAM,CAAC0D,OAAO,CAAChF,IAAI,IAAIsB,MAAM,CAAC4D,MAAM,CAAClF;UAAK;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAAC7B,WAAW;YAACwJ,SAAS;YAAApB,QAAA,gBAClBvG,OAAA,CAACzB,UAAU;cAACiI,OAAO,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClD9G,OAAA,CAACvB,YAAY;cACTmJ,aAAa;cAEbrF,IAAI,EAAC,SAAS;cACdsF,OAAO,EAAE1F,SAAU;cACnBc,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACX,OAAQ;cAC7BoD,QAAQ,EAAEA,CAAClC,CAAC,EAAE4C,GAAG,KAAK;gBAClBjE,MAAM,CAACmB,aAAa,CAAC,SAAS,EAAE8C,GAAG,CAAC;cACxC,CAAE;cACFR,KAAK,EAAEzD,MAAM,CAAC0D,OAAO,CAACvD,OAAO,IAAIwD,OAAO,CAAC3D,MAAM,CAAC4D,MAAM,CAACzD,OAAO,CAAE;cAChE0D,UAAU,EAAE7D,MAAM,CAAC0D,OAAO,CAACvD,OAAO,IAAIH,MAAM,CAAC4D,MAAM,CAACzD,OAAQ;cAC5D+D,cAAc,EAAGC,MAAM;gBAAA,IAAAC,YAAA;gBAAA,QAAAA,YAAA,GAAKD,MAAM,CAACzF,IAAI,cAAA0F,YAAA,cAAAA,YAAA,GAAI,EAAE;cAAA,CAAC;cAC9CC,YAAY,EAAEA,CAAC5H,KAAK,EAAE0H,MAAM,kBACxBhI,OAAA,CAAChC,GAAG;gBAACmK,SAAS,EAAC,IAAI;gBAAC1B,EAAE,EAAE;kBAAE,SAAS,EAAE;oBAAE2B,EAAE,EAAE,CAAC;oBAAEC,UAAU,EAAE;kBAAE;gBAAE,CAAE;gBAAA,GAAK/H,KAAK;gBAAAiG,QAAA,GACrEyB,MAAM,CAACvF,IAAI,EAAC,GAAC,EAACuF,MAAM,CAACzF,IAAI;cAAA;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CACP;cACFwB,WAAW,EAAG3C,MAAM,iBAAK3F,OAAA,CAAC3B,SAAS;gBAAA,GAAKsH,MAAM,CAAC4C,UAAU;gBAAA,GAAM5C;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAAC7B,WAAW;YAACwJ,SAAS;YAAApB,QAAA,gBAClBvG,OAAA,CAACzB,UAAU;cAACiI,OAAO,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvD9G,OAAA,CAAChC,GAAG;cAACyI,EAAE,EAAE;gBACL+B,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE;cACT,CAAE;cAAAlC,QAAA,gBACEvG,OAAA,CAAChC,GAAG;gBAACyI,EAAE,EAAE;kBAAEiC,KAAK,EAAE;gBAAG,CAAE;gBAAAnC,QAAA,eACnBvG,OAAA,CAACX,KAAK;kBACFoH,EAAE,EAAE;oBACAkC,SAAS,EAAE,QAAQ;oBACnB,iBAAiB,EAAE;sBACfC,SAAS,EAAEnH,KAAK,CAACoH,OAAO,CAACC,MAAM,CAACC;oBACpC;kBACJ,CAAE;kBAEFC,YAAY,EAAC,cAAc;kBAC3BzG,IAAI,EAAC,WAAW;kBAChB0G,cAAc,EAAC,GAAG;kBAClBjH,IAAI,EAAC,QAAQ;kBACbiB,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACnC,SAAU;kBAC/B4E,QAAQ,EAAEvD,MAAM,CAACwD;gBAAa;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACN9G,OAAA,CAACX,KAAK;gBAEFkD,IAAI,EAAC,aAAa;gBAClBU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACZ,WAAY;gBACjCqD,QAAQ,EAAEvD,MAAM,CAACwD,YAAa;gBAC9BC,KAAK,EAAEzD,MAAM,CAAC0D,OAAO,CAACxD,WAAW,IAAIyD,OAAO,CAAC3D,MAAM,CAAC4D,MAAM,CAAC1D,WAAW,CAAE;gBACxE2D,UAAU,EAAE7D,MAAM,CAAC0D,OAAO,CAACxD,WAAW,IAAIF,MAAM,CAAC4D,MAAM,CAAC1D;cAAY;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACX,KAAK;YACF8H,KAAK,EAAC,MAAM;YACZ5E,IAAI,EAAC,MAAM;YACXU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACV,IAAK;YAC1BmD,QAAQ,EAAEvD,MAAM,CAACwD,YAAa;YAC9BC,KAAK,EAAEzD,MAAM,CAAC0D,OAAO,CAACtD,IAAI,IAAIuD,OAAO,CAAC3D,MAAM,CAAC4D,MAAM,CAACxD,IAAI,CAAE;YAC1DyD,UAAU,EAAE7D,MAAM,CAAC0D,OAAO,CAACtD,IAAI,IAAIJ,MAAM,CAAC4D,MAAM,CAACxD;UAAK;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACX,KAAK;YACF8H,KAAK,EAAC,SAAS;YACf5E,IAAI,EAAC,SAAS;YACdU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACT,OAAQ;YAC7BkD,QAAQ,EAAEvD,MAAM,CAACwD,YAAa;YAC9BC,KAAK,EAAEzD,MAAM,CAAC0D,OAAO,CAACrD,OAAO,IAAIsD,OAAO,CAAC3D,MAAM,CAAC4D,MAAM,CAACvD,OAAO,CAAE;YAChEwD,UAAU,EAAE7D,MAAM,CAAC0D,OAAO,CAACrD,OAAO,IAAIL,MAAM,CAAC4D,MAAM,CAACvD;UAAQ;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACV,WAAW;YACR6H,KAAK,EAAC,YAAY;YAClB5E,IAAI,EAAC,YAAY;YACjBU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACR,UAAW;YAChCiD,QAAQ,EAAEvD,MAAM,CAACwD,YAAa;YAC9BC,KAAK,EAAEzD,MAAM,CAAC0D,OAAO,CAACpD,UAAU,IAAIqD,OAAO,CAAC3D,MAAM,CAAC4D,MAAM,CAACtD,UAAU,CAAE;YACtEuD,UAAU,EAAE7D,MAAM,CAAC0D,OAAO,CAACpD,UAAU,IAAIN,MAAM,CAAC4D,MAAM,CAACtD,UAAW;YAAAoC,QAAA,EAEjE7E,WAAW,CAACU,GAAG,CAAC,CAACC,IAAI,EAAE6G,KAAK,kBACzBlJ,OAAA,CAAC1B,QAAQ;cAAa2E,KAAK,EAAEZ,IAAI,CAAC+B,GAAI;cAAAmC,QAAA,EACjClE,IAAI,CAACE;YAAI,GADC2G,KAAK;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACV,WAAW;YACR6H,KAAK,EAAC,aAAa;YACnB5E,IAAI,EAAC,aAAa;YAClBU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACN,WAAY;YACjC+C,QAAQ,EAAEvD,MAAM,CAACwD,YAAa;YAC9BC,KAAK,EAAEzD,MAAM,CAAC0D,OAAO,CAAClD,WAAW,IAAImD,OAAO,CAAC3D,MAAM,CAAC4D,MAAM,CAACpD,WAAW,CAAE;YACxEqD,UAAU,EAAE7D,MAAM,CAAC0D,OAAO,CAAClD,WAAW,IAAIR,MAAM,CAAC4D,MAAM,CAACpD,WAAY;YAAAkC,QAAA,EAEnE3E,YAAY,CAACQ,GAAG,CAAC,CAACC,IAAI,EAAE6G,KAAK,kBAC1BlJ,OAAA,CAAC1B,QAAQ;cAAa2E,KAAK,EAAEZ,IAAI,CAAC+B,GAAI;cAAAmC,QAAA,EACjClE,IAAI,CAACE;YAAI,GADC2G,KAAK;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAGP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAAC7B,WAAW;YAACwJ,SAAS;YAAApB,QAAA,gBAClBvG,OAAA,CAACzB,UAAU;cAACiI,OAAO,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3D9G,OAAA,CAAChC,GAAG;cAACyI,EAAE,EAAE;gBACL+B,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,GAAG;gBACRU,UAAU,EAAE;cAChB,CAAE;cAAA5C,QAAA,gBACEvG,OAAA,CAACX,KAAK;gBAEF8H,KAAK,EAAC,EAAE;gBACR5E,IAAI,EAAC,WAAW;gBAChBU,KAAK,EAAEY,MAAM,CAACc,MAAM,CAACL,SAAU;gBAC/B8C,QAAQ,EAAExB,qBAAsB;gBAChCwD,WAAW,EAAEnH,WAAW,KAAK,SAAS,GAAG,UAAU,GAAG,WAAY;gBAClEqF,KAAK,EAAEzD,MAAM,CAAC0D,OAAO,CAACjD,SAAS,IAAIkD,OAAO,CAAC3D,MAAM,CAAC4D,MAAM,CAACnD,SAAS,CAAE;gBACpEoD,UAAU,EAAE7D,MAAM,CAAC0D,OAAO,CAACjD,SAAS,IAAIT,MAAM,CAAC4D,MAAM,CAACnD;cAAU;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAEtE9G,OAAA,CAAC/B,MAAM;gBACHoL,IAAI,EAAC,OAAO;gBACZ7C,OAAO,EAAC,UAAU;gBAClB8C,OAAO,EAAExD,iBAAkB;gBAAAS,QAAA,EAE1BtE,WAAW,KAAK,SAAS,GAAG,WAAW,GAAG;cAAa;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACN9G,OAAA,CAACzB,UAAU;cAACiI,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAE8C,EAAE,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAiB,CAAE;cAAAjD,QAAA,EAChEtE,WAAW,KAAK,SAAS,GAAG,qDAAqD,GAAG;YAAqC;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC,eACb9G,OAAA,CAACzB,UAAU;cAACiI,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAE8C,EAAE,EAAE,CAAC;gBAAEf,OAAO,EAAE,OAAO;gBAAEgB,KAAK,EAAE;cAAY,CAAE;cAAAjD,QAAA,EAAC;YAEnF;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAGP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACV,WAAW;YACR6H,KAAK,EAAC,mBAAmB;YACzB5E,IAAI,EAAC,+BAA+B;YACpCU,KAAK,EAAE,EAAA/B,qBAAA,GAAA2C,MAAM,CAACc,MAAM,CAACJ,YAAY,cAAArD,qBAAA,uBAA1BA,qBAAA,CAA4BuI,gBAAgB,KAAI,WAAY;YACnErC,QAAQ,EAAGlC,CAAC,IAAKe,wBAAwB,CAAC,kBAAkB,EAAEf,CAAC,CAACW,MAAM,CAAC5C,KAAK,CAAE;YAAAsD,QAAA,EAE7E3G,kBAAkB,CAACwC,GAAG,CAAEsH,QAAQ,iBAC7B1J,OAAA,CAAC1B,QAAQ;cAAsB2E,KAAK,EAAEyG,QAAQ,CAACzG,KAAM;cAAAsD,QAAA,EAChDmD,QAAQ,CAACvC;YAAK,GADJuC,QAAQ,CAACzG,KAAK;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEnB,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACX,KAAK;YACF8H,KAAK,EAAC,aAAa;YACnB5E,IAAI,EAAC,yBAAyB;YAC9BP,IAAI,EAAC,MAAM;YACXiB,KAAK,EACD,CAAA9B,sBAAA,GAAA0C,MAAM,CAACc,MAAM,CAACJ,YAAY,cAAApD,sBAAA,eAA1BA,sBAAA,CAA4BwI,UAAU,GAAI,OAAO9F,MAAM,CAACc,MAAM,CAACJ,YAAY,CAACoF,UAAU,KAAK,QAAQ,GAAG9F,MAAM,CAACc,MAAM,CAACJ,YAAY,CAACoF,UAAU,CAACrG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI+C,IAAI,CAACxC,MAAM,CAACc,MAAM,CAACJ,YAAY,CAACoF,UAAU,CAAC,CAACrD,WAAW,CAAC,CAAC,CAAChD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAI,YAC9O;YACD8D,QAAQ,EAAGlC,CAAC,IAAKe,wBAAwB,CAAC,YAAY,EAAEf,CAAC,CAACW,MAAM,CAAC5C,KAAK;UAAE;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACX,KAAK;YACF8H,KAAK,EAAC,WAAW;YACjB5E,IAAI,EAAC,uBAAuB;YAC5BP,IAAI,EAAC,MAAM;YACXiB,KAAK,EACD,CAAA7B,sBAAA,GAAAyC,MAAM,CAACc,MAAM,CAACJ,YAAY,cAAAnD,sBAAA,eAA1BA,sBAAA,CAA4BwI,QAAQ,GAAI,OAAO/F,MAAM,CAACc,MAAM,CAACJ,YAAY,CAACqF,QAAQ,KAAK,QAAQ,GAAG/F,MAAM,CAACc,MAAM,CAACJ,YAAY,CAACqF,QAAQ,CAACtG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI+C,IAAI,CAACxC,MAAM,CAACc,MAAM,CAACJ,YAAY,CAACqF,QAAQ,CAAC,CAACtD,WAAW,CAAC,CAAC,CAAChD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAI,YACtO;YACD8D,QAAQ,EAAGlC,CAAC,IAAKe,wBAAwB,CAAC,UAAU,EAAEf,CAAC,CAACW,MAAM,CAAC5C,KAAK;UAAE;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACV,WAAW;YACR6H,KAAK,EAAC,YAAY;YAClB5E,IAAI,EAAC,wBAAwB;YAC7BU,KAAK,EAAE,EAAA5B,sBAAA,GAAAwC,MAAM,CAACc,MAAM,CAACJ,YAAY,cAAAlD,sBAAA,uBAA1BA,sBAAA,CAA4BwI,SAAS,KAAI,OAAQ;YACxDzC,QAAQ,EAAGlC,CAAC,IAAKe,wBAAwB,CAAC,WAAW,EAAEf,CAAC,CAACW,MAAM,CAAC5C,KAAK,CAAE;YAAAsD,QAAA,EAEtEzG,YAAY,CAACsC,GAAG,CAAE4F,MAAM,iBACrBhI,OAAA,CAAC1B,QAAQ;cAAoB2E,KAAK,EAAE+E,MAAM,CAAC/E,KAAM;cAAAsD,QAAA,EAC5CyB,MAAM,CAACb;YAAK,GADFa,MAAM,CAAC/E,KAAK;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACV,WAAW;YACR6H,KAAK,EAAC,UAAU;YAChB5E,IAAI,EAAC,sBAAsB;YAC3BU,KAAK,EAAE,EAAA3B,sBAAA,GAAAuC,MAAM,CAACc,MAAM,CAACJ,YAAY,cAAAjD,sBAAA,uBAA1BA,sBAAA,CAA4BwI,OAAO,KAAI,OAAQ;YACtD1C,QAAQ,EAAGlC,CAAC,IAAKe,wBAAwB,CAAC,SAAS,EAAEf,CAAC,CAACW,MAAM,CAAC5C,KAAK,CAAE;YAAAsD,QAAA,EAEpEzG,YAAY,CAACsC,GAAG,CAAE4F,MAAM,iBACrBhI,OAAA,CAAC1B,QAAQ;cAAoB2E,KAAK,EAAE+E,MAAM,CAAC/E,KAAM;cAAAsD,QAAA,EAC5CyB,MAAM,CAACb;YAAK,GADFa,MAAM,CAAC/E,KAAK;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjB,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEP9G,OAAA,CAAC5B,IAAI;UAACiE,IAAI;UAAC4E,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBvG,OAAA,CAACX,KAAK;YACF8H,KAAK,EAAC,eAAe;YACrB5E,IAAI,EAAC,2BAA2B;YAChCP,IAAI,EAAC,QAAQ;YACb+H,IAAI,EAAC,KAAK;YACV9G,KAAK,EAAE,EAAA1B,sBAAA,GAAAsC,MAAM,CAACc,MAAM,CAACJ,YAAY,cAAAhD,sBAAA,uBAA1BA,sBAAA,CAA4ByI,YAAY,KAAI,GAAI;YACvD5C,QAAQ,EAAGlC,CAAC,IAAKe,wBAAwB,CAAC,cAAc,EAAET,UAAU,CAACN,CAAC,CAACW,MAAM,CAAC5C,KAAK,CAAC;UAAE;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAENrH,GAAG,CAACC,OAAO,CAACuK,OAAO,EAAEtK,QAAQ,CAACQ,IAAI,CAAC,iBAChCH,OAAA,CAAC5B,IAAI;UAACqI,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAClH,IAAI;UAAC0E,SAAS;UAACmD,cAAc,EAAC,UAAU;UAAA3D,QAAA,eACzDvG,OAAA,CAAC/B,MAAM;YACH+D,IAAI,EAAC,QAAQ;YACbwH,KAAK,EAAC,SAAS;YACfhD,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAExB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf;AAACvG,EAAA,CAvZuBN,gBAAgB;EAAA,QAEnBtB,WAAW,EACdH,QAAQ,EACFI,WAAW,EACVA,WAAW,EAChBA,WAAW,EAiDZQ,SAAS;AAAA;AAAA+K,EAAA,GAvDJlK,gBAAgB;AAAA,IAAAkK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}