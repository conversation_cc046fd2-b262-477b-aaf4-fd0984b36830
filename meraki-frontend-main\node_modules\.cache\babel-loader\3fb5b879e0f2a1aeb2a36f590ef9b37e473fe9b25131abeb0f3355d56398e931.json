{"ast": null, "code": "export const SCHEDULE_TEMPLATES = [{\n  value: 'day_shift',\n  label: 'Day Shift'\n}, {\n  value: 'night_shift',\n  label: 'Night Shift'\n}];\nexport const DEFAULT_WORK_SCHEDULE = {\n  scheduleTemplate: 'day_shift',\n  shiftStart: '2025-06-11',\n  shiftEnd: '2025-06-11',\n  startTime: '09:00',\n  endTime: '17:00',\n  minimumHours: 8.0\n};\nexport const TIME_OPTIONS = [];\nfor (let hour = 0; hour < 24; hour++) {\n  for (let minute = 0; minute < 60; minute += 30) {\n    const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;\n    TIME_OPTIONS.push({\n      value: timeString,\n      label: timeString\n    });\n  }\n}", "map": {"version": 3, "names": ["SCHEDULE_TEMPLATES", "value", "label", "DEFAULT_WORK_SCHEDULE", "scheduleTemplate", "shiftStart", "shiftEnd", "startTime", "endTime", "minimumHours", "TIME_OPTIONS", "hour", "minute", "timeString", "toString", "padStart", "push"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/constants/workSchedule.js"], "sourcesContent": ["export const SCHEDULE_TEMPLATES = [\n    { value: 'day_shift', label: 'Day Shift' },\n    { value: 'night_shift', label: 'Night Shift' }\n];\n\nexport const DEFAULT_WORK_SCHEDULE = {\n    scheduleTemplate: 'day_shift',\n    shiftStart: '2025-06-11',\n    shiftEnd: '2025-06-11', \n    startTime: '09:00',\n    endTime: '17:00',\n    minimumHours: 8.0\n};\n\nexport const TIME_OPTIONS = [];\nfor (let hour = 0; hour < 24; hour++) {\n    for (let minute = 0; minute < 60; minute += 30) {\n        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;\n        TIME_OPTIONS.push({\n            value: timeString,\n            label: timeString\n        });\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,kBAAkB,GAAG,CAC9B;EAAEC,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAY,CAAC,EAC1C;EAAED,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAc,CAAC,CACjD;AAED,OAAO,MAAMC,qBAAqB,GAAG;EACjCC,gBAAgB,EAAE,WAAW;EAC7BC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,YAAY;EACtBC,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE,OAAO;EAChBC,YAAY,EAAE;AAClB,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,EAAE;AAC9B,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,EAAE,EAAEA,IAAI,EAAE,EAAE;EAClC,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG,EAAE,EAAEA,MAAM,IAAI,EAAE,EAAE;IAC5C,MAAMC,UAAU,GAAG,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,MAAM,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC9FL,YAAY,CAACM,IAAI,CAAC;MACdf,KAAK,EAAEY,UAAU;MACjBX,KAAK,EAAEW;IACX,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}