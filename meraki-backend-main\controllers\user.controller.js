'use strict';

/**
 * User Controller
 *
 * This controller handles all user-related operations including:
 * - User creation, retrieval, update, and deletion
 * - User authentication and profile management
 * - User leave management
 */

const bcrypt = require("bcryptjs");
const UserService = require("../services/user.service");
const { USERS } = require("../data/default");
const fs = require('fs');

/**
 * Create default users if no users exist in the database
 * This is called during application initialization
 */
exports.createDefaultUsers = async () => {
    try {
        const users = await UserService.getUsersByQuery({});
        if (users.data.length === 0) {
            await UserService.createManyUsers(USERS);
        }
    } catch (error) {
        // Log error but don't crash the application
        console.error("Error creating default users:", error);
    }
};

/**
 * Fetch all users with optional filtering and pagination
 *
 * @param {Object} req - Express request object with query parameters
 * @param {Object} res - Express response object
 * @returns {Object} Response with users data and pagination info
 */
exports.fetchAllUsers = async (req, res) => {
    try {
        const { keyword, sort, page, limit, role, status, department, designation } = req.query;

        // Build query object with pagination and sorting
        const queries = {
            page: parseInt(page) || 1,
            limit: parseInt(limit) || 20,
            sort: { name: -1 },
            query: {}
        };

        // Add filters to query if provided
        if (keyword) {
            queries.query.name = { '$regex': '.*' + keyword + '.*', '$options': 'i' };
        }
        if (sort && sort.includes(",")) {
            const field = sort.split(",");
            queries.sort = { [field[0]]: parseInt(field[1]) || -1 };
        }
        if (role) {
            queries.query.role = Array.isArray(role) ? { $in: role } : role;
        }
        if (status && !isNaN(status)) {
            queries.query.status = parseInt(status);
        }
        if (department) {
            queries.query.department = department;
        }
        if (designation) {
            queries.query.designation = designation;
        }

        // Fetch users with the constructed query
        const results = await UserService.getUsersByQuery(queries);
        return res.status(200).send(results);
    } catch (error) {
        return res.status(500).send({
            message: "Failed to fetch users.",
            error: error.message
        });
    }
};

/**
 * Create a new user
 *
 * @param {Object} req - Express request object with user data in body
 * @param {Object} res - Express response object
 * @returns {Object} Response with success message or error
 */
exports.createUser = async (req, res) => {
    try {
        const { body } = req;

        // Hash the password for security
        body.password = bcrypt.hashSync(body.password, 8);

        // Convert comma-separated roles to array
        body.role = body.role.split(',');

        // Handle avatar upload if file is provided
        if (req.file) {
            const url = `${req.protocol}://${req.get('host')}/uploads/`;
            body.avatar = url + req.file.filename;
        }

        // Create the user in the database
        const result = await UserService.createUser(body);
        if (!result) {
            throw new Error("User creation failed");
        }

        return res.status(201).send({ message: "User created successfully." });
    } catch (error) {
        return res.status(500).send({
            message: "Failed to create user.",
            error: error.message
        });
    }
};

/**
 * Fetch a user by ID
 *
 * @param {Object} req - Express request object with user ID in params
 * @param {Object} res - Express response object
 * @returns {Object} Response with user data or error
 */
exports.fetchUserById = async (req, res) => {
    try {
        const { id } = req.params;

        // Fetch user from database
        const result = await UserService.getUserById(id);

        // Return 404 if user not found
        if (!result) {
            return res.status(404).send({
                message: "User not found"
            });
        }

        return res.status(200).send(result);
    } catch (error) {
        return res.status(500).send({
            message: "Failed to fetch user.",
            error: error.message
        });
    }
};

/**
 * Fetch the currently logged-in user's profile
 *
 * @param {Object} req - Express request object with user object from auth middleware
 * @param {Object} res - Express response object
 * @returns {Object} Response with user profile data or error
 */
exports.fetchLoggedInUser = async (req, res) => {
    try {
        const { user } = req;

        // Fetch user profile from database
        const result = await UserService.getUserById(user);

        // Return 404 if user not found
        if (!result) {
            return res.status(404).send({
                message: "User profile not found"
            });
        }

        return res.status(200).send(result);
    } catch (error) {
        return res.status(500).send({
            message: "Failed to fetch user profile.",
            error: error.message
        });
    }
};

/**
 * Update an existing user
 *
 * @param {Object} req - Express request object with user ID in params and update data in body
 * @param {Object} res - Express response object
 * @returns {Object} Response with success message or error
 */
exports.updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const body = { ...req.body };

        // Log the incoming request body for debugging
        console.log("Update user request body:", body);
        console.log("Work hours value:", body.workHours, "Type:", typeof body.workHours);
        console.log("Work schedule received:", body.workSchedule, "Type:", typeof body.workSchedule);
        console.log("Work schedule JSON:", JSON.stringify(body.workSchedule, null, 2));

        // Convert workHours to a number if it's a string
        if (body.workHours !== undefined) {
            // Parse as float to handle decimal values
            body.workHours = parseFloat(body.workHours);
            console.log("Converted work hours:", body.workHours, "Type:", typeof body.workHours);
        }

        // Handle work schedule processing if provided
        if (body.workSchedule !== undefined) {
            console.log("Processing work schedule:", body.workSchedule);

            // Ensure workSchedule is an object and not a string
            if (typeof body.workSchedule === 'string') {
                try {
                    body.workSchedule = JSON.parse(body.workSchedule);
                } catch (error) {
                    console.error("Failed to parse workSchedule JSON:", error);
                    // Set default workSchedule if parsing fails
                    body.workSchedule = {
                        scheduleTemplate: 'day_shift',
                        shiftStart: new Date(),
                        shiftEnd: new Date(),
                        startTime: '09:00',
                        endTime: '17:00',
                        minimumHours: 8.0
                    };
                }
            }

            // Validate and process workSchedule fields
            if (body.workSchedule && typeof body.workSchedule === 'object') {
                // Convert date strings to Date objects if needed
                if (body.workSchedule.shiftStart && typeof body.workSchedule.shiftStart === 'string') {
                    body.workSchedule.shiftStart = new Date(body.workSchedule.shiftStart);
                }
                if (body.workSchedule.shiftEnd && typeof body.workSchedule.shiftEnd === 'string') {
                    body.workSchedule.shiftEnd = new Date(body.workSchedule.shiftEnd);
                }

                // Ensure minimumHours is a number
                if (body.workSchedule.minimumHours !== undefined) {
                    body.workSchedule.minimumHours = parseFloat(body.workSchedule.minimumHours);
                }

                console.log("Processed work schedule:", body.workSchedule);
            }
        }

        // Hash password if provided
        if (body.password) {
            body.password = bcrypt.hashSync(body.password, 8);
        }

        // Convert comma-separated roles to array if provided
        if (body.role) {
            body.role = body.role.split(',');
        }

        // Handle avatar upload if file is provided
        if (req.file) {
            const url = `${req.protocol}://${req.get('host')}/uploads/`;
            body.avatar = url + req.file.filename;

            // Delete old avatar file if it exists
            const existingUser = await UserService.getUserById(id);
            if (existingUser && existingUser.avatar) {
                const imagePath = `./uploads/${existingUser.avatar.split('/').pop()}`;
                fs.unlink(imagePath, (err) => {
                    if (err) {
                        // Just log the error but continue with the update
                        console.error("Failed to delete old avatar:", err);
                    }
                });
            }
        }

        // Update user in database
        const result = await UserService.updateUser(id, body);
        console.log("Update result:", result);

        // Check if update was successful
        if (!result || result.n === 0) {
            return res.status(404).send({
                message: "User not found or update failed"
            });
        }

        return res.status(200).send({ message: "User updated successfully." });
    } catch (error) {
        return res.status(500).send({
            message: "Failed to update user.",
            error: error.message
        });
    }
};

/**
 * Delete a user
 *
 * @param {Object} req - Express request object with user ID in params
 * @param {Object} res - Express response object
 * @returns {Object} Response with success message or error
 */
exports.deleteUser = async (req, res) => {
    try {
        const { id } = req.params;

        // Delete user from database
        const result = await UserService.deleteUser(id);

        // Check if deletion was successful
        if (!result) {
            return res.status(404).send({
                message: "User not found or deletion failed"
            });
        }

        return res.status(200).send({ message: "User deleted successfully." });
    } catch (error) {
        return res.status(500).send({
            message: "Failed to delete user.",
            error: error.message
        });
    }
};

/**
 * Update a user's leave information
 *
 * @param {string} data - User ID
 * @param {Object} body - Leave data to update
 */
exports.updateUserLeave = async (data, body) => {
    try {
        await UserService.updateUserLeave(data, body);
    } catch (error) {
        // Log error but don't crash the application
        console.error("Error updating user leave:", error);
    }
};

/**
 * Update a user's leave information by admin
 *
 * @param {Object} req - Express request object with user ID and update data
 * @param {Object} res - Express response object
 * @returns {Object} Response with updated user data or error
 */
exports.updateUserByAdmin = async (req, res) => {
    try {
        const { id, body } = req;

        // Update user leave information
        const result = await UserService.updateUserLeaveAdmin(id, body);

        // Check if update was successful
        if (!result) {
            return res.status(404).send({
                message: "User not found or update failed"
            });
        }

        return res.status(200).send(result);
    } catch (error) {
        return res.status(500).send({
            message: "Failed to update user leave information.",
            error: error.message
        });
    }
};
