/**
 * API Utility Functions
 *
 * This module provides standardized functions for making API requests.
 * It handles authentication, error handling, and URL formatting.
 */

import axios from "axios";
import { getApiUrl } from "./apiConfig";

/**
 * Token key used for authentication in localStorage
 */
const TOKEN_KEY = "merakihr-token";

/**
 * Get the authentication token from localStorage
 *
 * @returns {string|null} The authentication token or null if not found
 */
const getAuthToken = () => localStorage.getItem(TOKEN_KEY);

/**
 * Create standard headers for API requests
 *
 * @param {string} contentType - The content type for the request
 * @returns {Object} Headers object with authorization and content type
 */
const createHeaders = (contentType = 'application/json') => {
    const token = getAuthToken();
    return {
        Authorization: token ? `Bearer ${token}` : undefined,
        'Content-Type': contentType
    };
};

/**
 * Format a URL to ensure it has the correct base API URL
 *
 * @param {string} url - The URL to format
 * @returns {string} The formatted URL with the correct base
 */
const formatUrl = (url) => {
    return url.includes('http://') || url.includes('https://') ? url : getApiUrl(url);
};

/**
 * Make a GET request to the API
 *
 * @param {string} url - The endpoint URL
 * @param {Object} query - Query parameters
 * @returns {Promise<Object>} The response data
 */
export async function get(url, query) {
    let queryString = "";

    if (query) {
        queryString = `?${new URLSearchParams(query).toString()}`;
    }

    const fullUrl = formatUrl(url);
    const headers = createHeaders();

    try {
        return await axios.get(fullUrl + queryString, { headers });
    } catch (error) {
        // Handle error and provide more context
        const errorMessage = error.response?.data?.message || error.message;
        const enhancedError = new Error(`GET request failed: ${errorMessage}`);
        enhancedError.originalError = error;
        enhancedError.status = error.response?.status;
        enhancedError.data = error.response?.data;
        throw enhancedError;
    }
}

/**
 * Make a POST request to the API
 *
 * @param {string} url - The endpoint URL
 * @param {Object} data - The data to send
 * @returns {Promise<Object>} The response data
 */
export async function post(url, data) {
    const formData = new FormData();

    // Properly handle different data types when adding to FormData
    Object.keys(data).forEach(key => {
        // Handle null or undefined
        if (data[key] === null || data[key] === undefined) {
            formData.append(key, '');
        }
        // Handle numeric values explicitly to ensure they're sent as numbers
        else if (typeof data[key] === 'number') {
            formData.append(key, data[key].toString());
        }
        // Handle boolean values
        else if (typeof data[key] === 'boolean') {
            formData.append(key, data[key] ? 'true' : 'false');
        }
        // Handle objects (like workSchedule) by JSON stringifying them
        else if (typeof data[key] === 'object' && data[key] !== null && !(data[key] instanceof File)) {
            formData.append(key, JSON.stringify(data[key]));
        }
        // Handle all other cases (strings, files, etc.)
        else {
            formData.append(key, data[key]);
        }
    });

    const fullUrl = formatUrl(url);
    const headers = createHeaders('multipart/form-data');

    try {
        return await axios.post(fullUrl, formData, { headers });
    } catch (error) {
        const errorMessage = error.response?.data?.message || error.message;
        const enhancedError = new Error(`POST request failed: ${errorMessage}`);
        enhancedError.originalError = error;
        enhancedError.status = error.response?.status;
        enhancedError.data = error.response?.data;
        throw enhancedError;
    }
}

/**
 * Make a PATCH request to the API
 *
 * @param {string} url - The endpoint URL
 * @param {Object} data - The data to send
 * @returns {Promise<Object>} The response data
 */
export async function patch(url, data) {
    const formData = new FormData();

    // Properly handle different data types when adding to FormData
    Object.keys(data).forEach(key => {
        // Handle null or undefined
        if (data[key] === null || data[key] === undefined) {
            formData.append(key, '');
        }
        // Handle numeric values explicitly to ensure they're sent as numbers
        else if (typeof data[key] === 'number') {
            formData.append(key, data[key].toString());
        }
        // Handle boolean values
        else if (typeof data[key] === 'boolean') {
            formData.append(key, data[key] ? 'true' : 'false');
        }
        // Handle objects (like workSchedule) by JSON stringifying them
        else if (typeof data[key] === 'object' && data[key] !== null && !(data[key] instanceof File)) {
            formData.append(key, JSON.stringify(data[key]));
        }
        // Handle all other cases (strings, files, etc.)
        else {
            formData.append(key, data[key]);
        }
    });

    // Log the form data for debugging
    console.log('Sending data in patch request:', Object.fromEntries(formData.entries()));

    const fullUrl = formatUrl(url);
    const headers = createHeaders('multipart/form-data');

    try {
        return await axios.patch(fullUrl, formData, { headers });
    } catch (error) {
        const errorMessage = error.response?.data?.message || error.message;
        const enhancedError = new Error(`PATCH request failed: ${errorMessage}`);
        enhancedError.originalError = error;
        enhancedError.status = error.response?.status;
        enhancedError.data = error.response?.data;
        throw enhancedError;
    }
}

/**
 * Make a DELETE request to the API
 *
 * @param {string} url - The endpoint URL
 * @returns {Promise<Object>} The response data
 */
export async function del(url) {
    const fullUrl = formatUrl(url);
    const headers = createHeaders();

    try {
        return await axios.delete(fullUrl, { headers });
    } catch (error) {
        const errorMessage = error.response?.data?.message || error.message;
        const enhancedError = new Error(`DELETE request failed: ${errorMessage}`);
        enhancedError.originalError = error;
        enhancedError.status = error.response?.status;
        enhancedError.data = error.response?.data;
        throw enhancedError;
    }
}
