{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\User\\\\components\\\\Form\\\\BasicInformation.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Button, Card, FormControl, Grid, InputBase, MenuItem, Typography, useTheme, Checkbox, FormControlLabel, FormGroup, Chip } from \"@mui/material\";\nimport { Autocomplete } from \"@mui/lab\";\nimport COUNTRIES from \"constants/countries\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { DepartmentSelector, DesignationSelector, GeneralSelector } from \"selectors\";\nimport { DepartmentActions, DesignationActions, GeneralActions, UserActions } from \"slices/actions\";\nimport { useFormik } from \"formik\";\nimport Input from \"components/Input\";\nimport SelectField from \"components/SelectField\";\nimport { toast } from \"react-toastify\";\nimport PropTypes from \"prop-types\";\nimport Can from \"../../../../utils/can\";\nimport { actions, features } from \"../../../../constants/permission\";\nimport { WORK_DAYS, DEFAULT_WORK_SCHEDULE, TIMEZONES, TIME_OPTIONS, BREAK_DURATION_OPTIONS } from \"../../../../constants/workSchedule\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nBasicInformation.propTypes = {\n  user: PropTypes.object,\n  form: PropTypes.object\n};\nexport default function BasicInformation(props) {\n  _s();\n  var _user$name, _user$country, _user$city, _user$address, _user$department$_id, _user$department, _user$designation$_id, _user$designation, _user$workHours;\n  const {\n    user,\n    form\n  } = props;\n  const dispatch = useDispatch();\n  const theme = useTheme();\n  const departments = useSelector(DepartmentSelector.getDepartments());\n  const designations = useSelector(DesignationSelector.getDesignations());\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  const [hoursFormat, setHoursFormat] = useState(\"decimal\"); // \"decimal\" or \"hhmm\"\n\n  const countries = COUNTRIES.map(item => ({\n    id: item.id,\n    name: item.name,\n    phoneCode: item.phoneCode,\n    flag: item.flag\n  }));\n  useEffect(() => {\n    dispatch(DepartmentActions.getDepartments());\n    dispatch(DesignationActions.getDesignations());\n  }, []);\n  useEffect(() => {\n    if (success) {\n      var _success$message;\n      toast.success(`${(_success$message = success === null || success === void 0 ? void 0 : success.message) !== null && _success$message !== void 0 ? _success$message : \"Success\"}`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true\n      });\n      dispatch(GeneralActions.removeSuccess(UserActions.updateUser.type));\n    }\n  }, [success]);\n\n  // Helper function to convert between decimal and HH:MM formats\n  const convertWorkHours = (value, toFormat) => {\n    if (!value) {\n      return \"\";\n    }\n    if (toFormat === \"decimal\") {\n      // Convert from HH:MM to decimal\n      if (value.includes(\":\")) {\n        const [hours, minutes] = value.split(\":\");\n        return Number(hours) + Number(minutes) / 60;\n      }\n      return value;\n    } else {\n      // Convert from decimal to HH:MM\n      const hours = Math.floor(Number(value));\n      const minutes = Math.round((Number(value) - hours) * 60);\n      return `${hours}:${minutes.toString().padStart(2, '0')}`;\n    }\n  };\n  const formik = useFormik({\n    initialValues: {\n      name: (_user$name = user === null || user === void 0 ? void 0 : user.name) !== null && _user$name !== void 0 ? _user$name : \"\",\n      phoneCode: '',\n      phoneNumber: \"\",\n      country: (_user$country = user === null || user === void 0 ? void 0 : user.country) !== null && _user$country !== void 0 ? _user$country : \"\",\n      city: (_user$city = user === null || user === void 0 ? void 0 : user.city) !== null && _user$city !== void 0 ? _user$city : \"\",\n      address: (_user$address = user === null || user === void 0 ? void 0 : user.address) !== null && _user$address !== void 0 ? _user$address : \"\",\n      department: (_user$department$_id = user === null || user === void 0 ? void 0 : (_user$department = user.department) === null || _user$department === void 0 ? void 0 : _user$department._id) !== null && _user$department$_id !== void 0 ? _user$department$_id : \"\",\n      designation: (_user$designation$_id = user === null || user === void 0 ? void 0 : (_user$designation = user.designation) === null || _user$designation === void 0 ? void 0 : _user$designation._id) !== null && _user$designation$_id !== void 0 ? _user$designation$_id : \"\",\n      workHours: (_user$workHours = user === null || user === void 0 ? void 0 : user.workHours) !== null && _user$workHours !== void 0 ? _user$workHours : \"8.5\" // Default to 8.5 hours or use stored value\n    },\n    enableReinitialize: true,\n    validateOnChange: true,\n    onSubmit: values => {\n      handleSubmit(values);\n    }\n  });\n  useEffect(() => {\n    var _formik$values$countr;\n    const code = (_formik$values$countr = formik.values.country) === null || _formik$values$countr === void 0 ? void 0 : _formik$values$countr.phoneCode;\n    const phone = formik.values.phone;\n    formik.setFieldValue('phoneCode', code !== null && code !== void 0 ? code : '');\n    formik.setFieldValue('phone', phone);\n  }, [formik.values.country]);\n  useEffect(() => {\n    const phone = user.phone;\n    const country = countries.find(e => e.name === user.country);\n    if (country) {\n      formik.setFieldValue('country', country);\n    }\n    if (phone && country) {\n      var _code$length;\n      const code = country.phoneCode;\n      formik.setFieldValue('phoneCode', code !== null && code !== void 0 ? code : '');\n      formik.setFieldValue('phoneNumber', phone.substring((_code$length = code.length) !== null && _code$length !== void 0 ? _code$length : 0));\n    }\n  }, [user]);\n  const handleSubmit = values => {\n    // Convert work hours to decimal format for storage\n    const workHoursDecimal = hoursFormat === \"decimal\" ? values.workHours : convertWorkHours(values.workHours, \"decimal\");\n\n    // Ensure workHours is a number, not a string\n    const workHoursNumber = parseFloat(workHoursDecimal);\n\n    // Log the value for debugging\n    console.log(\"Work hours being saved:\", workHoursNumber, typeof workHoursNumber);\n    const params = {\n      id: user._id,\n      ...values,\n      ...form,\n      phone: values.phoneCode + values.phoneNumber,\n      workHours: workHoursNumber\n    };\n    dispatch(UserActions.updateUser(params));\n  };\n  const handleWorkHoursChange = e => {\n    const {\n      value\n    } = e.target;\n    formik.setFieldValue('workHours', value);\n  };\n  const toggleHoursFormat = () => {\n    const newFormat = hoursFormat === \"decimal\" ? \"hhmm\" : \"decimal\";\n    const convertedValue = convertWorkHours(formik.values.workHours, newFormat);\n    setHoursFormat(newFormat);\n    formik.setFieldValue('workHours', convertedValue);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      sx: {\n        mb: 4\n      },\n      children: \"Basic Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: formik.handleSubmit,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Full Name\",\n            name: \"name\",\n            value: formik.values.name,\n            onChange: formik.handleChange,\n            error: formik.touched.name && Boolean(formik.errors.name),\n            helperText: formik.touched.name && formik.errors.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Country\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n              disablePortal: true,\n              name: \"country\",\n              options: countries,\n              value: formik.values.country,\n              onChange: (e, val) => {\n                formik.setFieldValue('country', val);\n              },\n              error: formik.touched.country && Boolean(formik.errors.country),\n              helperText: formik.touched.country && formik.errors.country,\n              getOptionLabel: option => {\n                var _option$name;\n                return (_option$name = option.name) !== null && _option$name !== void 0 ? _option$name : '';\n              },\n              renderOption: (props, option) => /*#__PURE__*/_jsxDEV(Box, {\n                component: \"li\",\n                sx: {\n                  '& > img': {\n                    mr: 2,\n                    flexShrink: 0\n                  }\n                },\n                ...props,\n                children: [option.flag, \" \", option.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 37\n              }, this),\n              renderInput: params => /*#__PURE__*/_jsxDEV(InputBase, {\n                ...params.InputProps,\n                ...params\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 58\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: 80\n                },\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  sx: {\n                    textAlign: 'center',\n                    '& .Mui-disabled': {\n                      fillColor: theme.palette.common.black\n                    }\n                  },\n                  autoComplete: \"new-password\",\n                  name: \"phoneCode\",\n                  startAdornment: \"+\",\n                  type: \"number\",\n                  value: formik.values.phoneCode,\n                  onChange: formik.handleChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                name: \"phoneNumber\",\n                value: formik.values.phoneNumber,\n                onChange: formik.handleChange,\n                error: formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber),\n                helperText: formik.touched.phoneNumber && formik.errors.phoneNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"City\",\n            name: \"city\",\n            value: formik.values.city,\n            onChange: formik.handleChange,\n            error: formik.touched.city && Boolean(formik.errors.city),\n            helperText: formik.touched.city && formik.errors.city\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Address\",\n            name: \"address\",\n            value: formik.values.address,\n            onChange: formik.handleChange,\n            error: formik.touched.address && Boolean(formik.errors.address),\n            helperText: formik.touched.address && formik.errors.address\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Department\",\n            name: \"department\",\n            value: formik.values.department,\n            onChange: formik.handleChange,\n            error: formik.touched.department && Boolean(formik.errors.department),\n            helperText: formik.touched.department && formik.errors.department,\n            children: departments.map((item, index) => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: item._id,\n              children: item.name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(SelectField, {\n            label: \"Designation\",\n            name: \"designation\",\n            value: formik.values.designation,\n            onChange: formik.handleChange,\n            error: formik.touched.designation && Boolean(formik.errors.designation),\n            helperText: formik.touched.designation && formik.errors.designation,\n            children: designations.map((item, index) => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: item._id,\n              children: item.name\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          lg: 6,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              children: \"Daily Work Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 1.5,\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                label: \"\",\n                name: \"workHours\",\n                value: formik.values.workHours,\n                onChange: handleWorkHoursChange,\n                placeholder: hoursFormat === \"decimal\" ? \"e.g. 8.5\" : \"e.g. 8:30\",\n                error: formik.touched.workHours && Boolean(formik.errors.workHours),\n                helperText: formik.touched.workHours && formik.errors.workHours\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                variant: \"outlined\",\n                onClick: toggleHoursFormat,\n                children: hoursFormat === \"decimal\" ? \"Use HH:MM\" : \"Use Decimal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                mt: 1,\n                color: 'text.secondary'\n              },\n              children: hoursFormat === \"decimal\" ? \"Enter as decimal (e.g., 8.5 for 8 hours 30 minutes)\" : \"Enter as hours:minutes (e.g., 8:30)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                mt: 1,\n                display: 'block',\n                color: 'info.main'\n              },\n              children: \"Note: This value determines your daily work requirement. Working less than half of this time will be marked as a half-day. Working more will count as overtime.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 21\n        }, this), Can(actions.readAll, features.user) && /*#__PURE__*/_jsxDEV(Grid, {\n          sx: {\n            mt: 3\n          },\n          item: true,\n          container: true,\n          justifyContent: \"flex-end\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            color: \"primary\",\n            variant: \"contained\",\n            children: \"Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 9\n  }, this);\n}\n_s(BasicInformation, \"nVcy5cbiLXR/E+141lGOoqWoQyE=\", false, function () {\n  return [useDispatch, useTheme, useSelector, useSelector, useSelector, useFormik];\n});\n_c = BasicInformation;\nvar _c;\n$RefreshReg$(_c, \"BasicInformation\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "<PERSON><PERSON>", "Card", "FormControl", "Grid", "InputBase", "MenuItem", "Typography", "useTheme", "Checkbox", "FormControlLabel", "FormGroup", "Chip", "Autocomplete", "COUNTRIES", "useDispatch", "useSelector", "DepartmentSelector", "DesignationSelector", "GeneralSelector", "DepartmentActions", "DesignationActions", "GeneralActions", "UserActions", "useFormik", "Input", "SelectField", "toast", "PropTypes", "Can", "actions", "features", "WORK_DAYS", "DEFAULT_WORK_SCHEDULE", "TIMEZONES", "TIME_OPTIONS", "BREAK_DURATION_OPTIONS", "jsxDEV", "_jsxDEV", "BasicInformation", "propTypes", "user", "object", "form", "props", "_s", "_user$name", "_user$country", "_user$city", "_user$address", "_user$department$_id", "_user$department", "_user$designation$_id", "_user$designation", "_user$workHours", "dispatch", "theme", "departments", "getDepartments", "designations", "getDesignations", "success", "updateUser", "type", "hoursFormat", "setHoursFormat", "countries", "map", "item", "id", "name", "phoneCode", "flag", "_success$message", "message", "position", "autoClose", "closeOnClick", "removeSuccess", "convertWorkHours", "value", "toFormat", "includes", "hours", "minutes", "split", "Number", "Math", "floor", "round", "toString", "padStart", "formik", "initialValues", "phoneNumber", "country", "city", "address", "department", "_id", "designation", "workHours", "enableReinitialize", "validateOnChange", "onSubmit", "values", "handleSubmit", "_formik$values$countr", "code", "phone", "setFieldValue", "find", "e", "_code$length", "substring", "length", "workHoursDecimal", "workHoursNumber", "parseFloat", "console", "log", "params", "handleWorkHoursChange", "target", "toggleHoursFormat", "newFormat", "convertedValue", "children", "variant", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "lg", "xs", "label", "onChange", "handleChange", "error", "touched", "Boolean", "errors", "helperText", "fullWidth", "disable<PERSON><PERSON><PERSON>", "options", "val", "getOptionLabel", "option", "_option$name", "renderOption", "component", "mr", "flexShrink", "renderInput", "InputProps", "display", "gap", "width", "textAlign", "fillColor", "palette", "common", "black", "autoComplete", "startAdornment", "index", "alignItems", "placeholder", "size", "onClick", "mt", "color", "readAll", "justifyContent", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/User/components/Form/BasicInformation.js"], "sourcesContent": ["import React, {useEffect, useState} from \"react\";\r\nimport {\r\n    Box,\r\n    Button,\r\n    Card,\r\n    FormControl,\r\n    Grid,\r\n    InputBase,\r\n    MenuItem,\r\n    Typography,\r\n    useTheme,\r\n    Checkbox,\r\n    FormControlLabel,\r\n    FormGroup,\r\n    Chip\r\n} from \"@mui/material\";\r\nimport {Autocomplete} from \"@mui/lab\";\r\nimport COUNTRIES from \"constants/countries\";\r\nimport {useDispatch, useSelector} from \"react-redux\";\r\nimport {DepartmentSelector, DesignationSelector, GeneralSelector} from \"selectors\";\r\nimport {DepartmentActions, DesignationActions, GeneralActions, UserActions} from \"slices/actions\";\r\nimport {useFormik} from \"formik\";\r\nimport Input from \"components/Input\";\r\nimport SelectField from \"components/SelectField\";\r\nimport {toast} from \"react-toastify\";\r\nimport PropTypes from \"prop-types\";\r\nimport Can from \"../../../../utils/can\";\r\nimport {actions, features} from \"../../../../constants/permission\";\r\nimport {WORK_DAYS, DEFAULT_WORK_SCHEDULE, TIMEZONES, TIME_OPTIONS, BREAK_DURATION_OPTIONS} from \"../../../../constants/workSchedule\";\r\n\r\nBasicInformation.propTypes = {\r\n    user: PropTypes.object,\r\n    form: PropTypes.object\r\n};\r\n\r\nexport default function BasicInformation(props) {\r\n    const { user, form } = props;\r\n    const dispatch = useDispatch();\r\n    const theme = useTheme();\r\n    const departments = useSelector(DepartmentSelector.getDepartments());\r\n    const designations = useSelector(DesignationSelector.getDesignations());\r\n    const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\r\n\r\n    const [hoursFormat, setHoursFormat] = useState(\"decimal\"); // \"decimal\" or \"hhmm\"\r\n\r\n    const countries = COUNTRIES.map(item => ({\r\n        id: item.id,\r\n        name: item.name,\r\n        phoneCode: item.phoneCode,\r\n        flag: item.flag\r\n    }));\r\n\r\n    useEffect(() => {\r\n        dispatch(DepartmentActions.getDepartments());\r\n        dispatch(DesignationActions.getDesignations());\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (success) {\r\n            toast.success(`${success?.message ?? \"Success\"}`, {\r\n                    position: \"top-right\",\r\n                    autoClose: 3000,\r\n                    closeOnClick: true,\r\n                });\r\n\r\n            dispatch(GeneralActions.removeSuccess(UserActions.updateUser.type));\r\n        }\r\n    }, [success]);\r\n\r\n    // Helper function to convert between decimal and HH:MM formats\r\n    const convertWorkHours = (value, toFormat) => {\r\n        if (!value) {\r\n            return \"\";\r\n        }\r\n\r\n        if (toFormat === \"decimal\") {\r\n            // Convert from HH:MM to decimal\r\n            if (value.includes(\":\")) {\r\n                const [hours, minutes] = value.split(\":\");\r\n                return Number(hours) + (Number(minutes) / 60);\r\n            }\r\n            return value;\r\n        } else {\r\n            // Convert from decimal to HH:MM\r\n            const hours = Math.floor(Number(value));\r\n            const minutes = Math.round((Number(value) - hours) * 60);\r\n            return `${hours}:${minutes.toString().padStart(2, '0')}`;\r\n        }\r\n    };\r\n\r\n    const formik = useFormik({\r\n        initialValues: {\r\n            name: user?.name ?? \"\",\r\n            phoneCode: '',\r\n            phoneNumber: \"\",\r\n            country: user?.country ?? \"\",\r\n            city: user?.city ?? \"\",\r\n            address: user?.address ?? \"\",\r\n            department: user?.department?._id ?? \"\",\r\n            designation: user?.designation?._id ?? \"\",\r\n            workHours: user?.workHours ?? \"8.5\", // Default to 8.5 hours or use stored value\r\n        },\r\n        enableReinitialize: true,\r\n        validateOnChange: true,\r\n        onSubmit: (values) => {\r\n            handleSubmit(values);\r\n        }\r\n    });\r\n\r\n    useEffect(() => {\r\n        const code = formik.values.country?.phoneCode;\r\n        const phone = formik.values.phone;\r\n\r\n        formik.setFieldValue('phoneCode', code ?? '');\r\n        formik.setFieldValue('phone', phone);\r\n    }, [formik.values.country]);\r\n\r\n    useEffect(() => {\r\n        const phone = user.phone;\r\n        const country = countries.find(e => e.name === user.country);\r\n\r\n        if (country) {\r\n            formik.setFieldValue('country', country);\r\n        }\r\n\r\n        if (phone && country) {\r\n            const code = country.phoneCode;\r\n\r\n            formik.setFieldValue('phoneCode', code ?? '');\r\n            formik.setFieldValue('phoneNumber', phone.substring(code.length ?? 0));\r\n        }\r\n    }, [user]);\r\n\r\n    const handleSubmit = (values) => {\r\n        // Convert work hours to decimal format for storage\r\n        const workHoursDecimal = hoursFormat === \"decimal\" ? values.workHours : convertWorkHours(values.workHours, \"decimal\");\r\n\r\n        // Ensure workHours is a number, not a string\r\n        const workHoursNumber = parseFloat(workHoursDecimal);\r\n\r\n        // Log the value for debugging\r\n        console.log(\"Work hours being saved:\", workHoursNumber, typeof workHoursNumber);\r\n\r\n        const params = {\r\n            id: user._id,\r\n            ...values,\r\n            ...form,\r\n            phone: values.phoneCode + values.phoneNumber,\r\n            workHours: workHoursNumber\r\n        };\r\n\r\n        dispatch(UserActions.updateUser(params));\r\n    }\r\n\r\n    const handleWorkHoursChange = (e) => {\r\n        const { value } = e.target;\r\n        formik.setFieldValue('workHours', value);\r\n    };\r\n\r\n    const toggleHoursFormat = () => {\r\n        const newFormat = hoursFormat === \"decimal\" ? \"hhmm\" : \"decimal\";\r\n        const convertedValue = convertWorkHours(formik.values.workHours, newFormat);\r\n        setHoursFormat(newFormat);\r\n        formik.setFieldValue('workHours', convertedValue);\r\n    };\r\n\r\n    return (\r\n        <Card>\r\n            <Typography variant='h5' sx={{ mb: 4 }}>Basic Information</Typography>\r\n            <form onSubmit={formik.handleSubmit}>\r\n                <Grid container spacing={2}>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n\r\n                            label=\"Full Name\"\r\n                            name='name'\r\n                            value={formik.values.name}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.name && Boolean(formik.errors.name)}\r\n                            helperText={formik.touched.name && formik.errors.name}/>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <FormControl fullWidth>\r\n                            <Typography variant='caption'>Country</Typography>\r\n                            <Autocomplete\r\n                                disablePortal\r\n\r\n                                name='country'\r\n                                options={countries}\r\n                                value={formik.values.country}\r\n                                onChange={(e, val) => {\r\n                                    formik.setFieldValue('country', val);\r\n                                }}\r\n                                error={formik.touched.country && Boolean(formik.errors.country)}\r\n                                helperText={formik.touched.country && formik.errors.country}\r\n                                getOptionLabel={(option) => option.name ?? ''}\r\n                                renderOption={(props, option) => (\r\n                                    <Box component=\"li\" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>\r\n                                        {option.flag} {option.name}\r\n                                    </Box>\r\n                                )}\r\n                                renderInput={(params) => <InputBase {...params.InputProps} {...params} />}\r\n                            />\r\n                        </FormControl>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <FormControl fullWidth>\r\n                            <Typography variant='caption'>Phone Number</Typography>\r\n                            <Box sx={{\r\n                                display: 'flex',\r\n                                gap: 1.5\r\n                            }}>\r\n                                <Box sx={{ width: 80 }}>\r\n                                    <Input\r\n                                        sx={{\r\n                                            textAlign: 'center',\r\n                                            '& .Mui-disabled': {\r\n                                                fillColor: theme.palette.common.black\r\n                                            }\r\n                                        }}\r\n\r\n                                        autoComplete='new-password'\r\n                                        name='phoneCode'\r\n                                        startAdornment='+'\r\n                                        type='number'\r\n                                        value={formik.values.phoneCode}\r\n                                        onChange={formik.handleChange}/>\r\n                                </Box>\r\n                                <Input\r\n\r\n                                    name='phoneNumber'\r\n                                    value={formik.values.phoneNumber}\r\n                                    onChange={formik.handleChange}\r\n                                    error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}\r\n                                    helperText={formik.touched.phoneNumber && formik.errors.phoneNumber}/>\r\n                            </Box>\r\n                        </FormControl>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"City\"\r\n                            name='city'\r\n                            value={formik.values.city}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.city && Boolean(formik.errors.city)}\r\n                            helperText={formik.touched.city && formik.errors.city}\r\n                            />\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <Input\r\n                            label=\"Address\"\r\n                            name='address'\r\n                            value={formik.values.address}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.address && Boolean(formik.errors.address)}\r\n                            helperText={formik.touched.address && formik.errors.address}\r\n                            />\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Department\"\r\n                            name='department'\r\n                            value={formik.values.department}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.department && Boolean(formik.errors.department)}\r\n                            helperText={formik.touched.department && formik.errors.department}\r\n                            >\r\n                            {departments.map((item, index) => (\r\n                                <MenuItem key={index} value={item._id}>\r\n                                    {item.name}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n                    <Grid item lg={6} xs={12}>\r\n                        <SelectField\r\n                            label=\"Designation\"\r\n                            name='designation'\r\n                            value={formik.values.designation}\r\n                            onChange={formik.handleChange}\r\n                            error={formik.touched.designation && Boolean(formik.errors.designation)}\r\n                            helperText={formik.touched.designation && formik.errors.designation}\r\n                            >\r\n                            {designations.map((item, index) => (\r\n                                <MenuItem key={index} value={item._id}>\r\n                                    {item.name}\r\n                                </MenuItem>\r\n                            ))}\r\n                        </SelectField>\r\n                    </Grid>\r\n\r\n                    {/* New Work Hours Field */}\r\n                    <Grid item lg={6} xs={12}>\r\n                        <FormControl fullWidth>\r\n                            <Typography variant='caption'>Daily Work Hours</Typography>\r\n                            <Box sx={{\r\n                                display: 'flex',\r\n                                gap: 1.5,\r\n                                alignItems: 'center'\r\n                            }}>\r\n                                <Input\r\n\r\n                                    label=\"\"\r\n                                    name='workHours'\r\n                                    value={formik.values.workHours}\r\n                                    onChange={handleWorkHoursChange}\r\n                                    placeholder={hoursFormat === \"decimal\" ? \"e.g. 8.5\" : \"e.g. 8:30\"}\r\n                                    error={formik.touched.workHours && Boolean(formik.errors.workHours)}\r\n                                    helperText={formik.touched.workHours && formik.errors.workHours}/>\r\n\r\n                                <Button\r\n                                    size=\"small\"\r\n                                    variant=\"outlined\"\r\n                                    onClick={toggleHoursFormat}\r\n                                    >\r\n                                    {hoursFormat === \"decimal\" ? \"Use HH:MM\" : \"Use Decimal\"}\r\n                                </Button>\r\n                            </Box>\r\n                            <Typography variant='caption' sx={{ mt: 1, color: 'text.secondary' }}>\r\n                                {hoursFormat === \"decimal\" ? \"Enter as decimal (e.g., 8.5 for 8 hours 30 minutes)\" : \"Enter as hours:minutes (e.g., 8:30)\"}\r\n                            </Typography>\r\n                            <Typography variant='caption' sx={{ mt: 1, display: 'block', color: 'info.main' }}>\r\n                                Note: This value determines your daily work requirement. Working less than half of this time will be marked as a half-day. Working more will count as overtime.\r\n                            </Typography>\r\n                        </FormControl>\r\n                    </Grid>\r\n\r\n                    {Can(actions.readAll, features.user) && (\r\n                        <Grid sx={{ mt: 3 }} item container justifyContent=\"flex-end\">\r\n                            <Button\r\n                                type=\"submit\"\r\n                                color=\"primary\"\r\n                                variant=\"contained\">\r\n                                Submit\r\n                            </Button>\r\n                        </Grid>\r\n                    )}\r\n                </Grid>\r\n            </form>\r\n        </Card>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,SAAS,EAAEC,QAAQ,QAAO,OAAO;AAChD,SACIC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRC,gBAAgB,EAChBC,SAAS,EACTC,IAAI,QACD,eAAe;AACtB,SAAQC,YAAY,QAAO,UAAU;AACrC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAAQC,WAAW,EAAEC,WAAW,QAAO,aAAa;AACpD,SAAQC,kBAAkB,EAAEC,mBAAmB,EAAEC,eAAe,QAAO,WAAW;AAClF,SAAQC,iBAAiB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,QAAO,gBAAgB;AACjG,SAAQC,SAAS,QAAO,QAAQ;AAChC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAAQC,KAAK,QAAO,gBAAgB;AACpC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAAQC,OAAO,EAAEC,QAAQ,QAAO,kCAAkC;AAClE,SAAQC,SAAS,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,sBAAsB,QAAO,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErIC,gBAAgB,CAACC,SAAS,GAAG;EACzBC,IAAI,EAAEb,SAAS,CAACc,MAAM;EACtBC,IAAI,EAAEf,SAAS,CAACc;AACpB,CAAC;AAED,eAAe,SAASH,gBAAgBA,CAACK,KAAK,EAAE;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,eAAA;EAC5C,MAAM;IAAEb,IAAI;IAAEE;EAAK,CAAC,GAAGC,KAAK;EAC5B,MAAMW,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,KAAK,GAAGhD,QAAQ,CAAC,CAAC;EACxB,MAAMiD,WAAW,GAAGzC,WAAW,CAACC,kBAAkB,CAACyC,cAAc,CAAC,CAAC,CAAC;EACpE,MAAMC,YAAY,GAAG3C,WAAW,CAACE,mBAAmB,CAAC0C,eAAe,CAAC,CAAC,CAAC;EACvE,MAAMC,OAAO,GAAG7C,WAAW,CAACG,eAAe,CAAC0C,OAAO,CAACtC,WAAW,CAACuC,UAAU,CAACC,IAAI,CAAC,CAAC;EAEjF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;;EAE3D,MAAMmE,SAAS,GAAGpD,SAAS,CAACqD,GAAG,CAACC,IAAI,KAAK;IACrCC,EAAE,EAAED,IAAI,CAACC,EAAE;IACXC,IAAI,EAAEF,IAAI,CAACE,IAAI;IACfC,SAAS,EAAEH,IAAI,CAACG,SAAS;IACzBC,IAAI,EAAEJ,IAAI,CAACI;EACf,CAAC,CAAC,CAAC;EAEH1E,SAAS,CAAC,MAAM;IACZyD,QAAQ,CAACnC,iBAAiB,CAACsC,cAAc,CAAC,CAAC,CAAC;IAC5CH,QAAQ,CAAClC,kBAAkB,CAACuC,eAAe,CAAC,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EAEN9D,SAAS,CAAC,MAAM;IACZ,IAAI+D,OAAO,EAAE;MAAA,IAAAY,gBAAA;MACT9C,KAAK,CAACkC,OAAO,CAAC,IAAAY,gBAAA,GAAGZ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,OAAO,cAAAD,gBAAA,cAAAA,gBAAA,GAAI,SAAS,EAAE,EAAE;QAC1CE,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE;MAClB,CAAC,CAAC;MAENtB,QAAQ,CAACjC,cAAc,CAACwD,aAAa,CAACvD,WAAW,CAACuC,UAAU,CAACC,IAAI,CAAC,CAAC;IACvE;EACJ,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMkB,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC1C,IAAI,CAACD,KAAK,EAAE;MACR,OAAO,EAAE;IACb;IAEA,IAAIC,QAAQ,KAAK,SAAS,EAAE;MACxB;MACA,IAAID,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;QACrB,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGJ,KAAK,CAACK,KAAK,CAAC,GAAG,CAAC;QACzC,OAAOC,MAAM,CAACH,KAAK,CAAC,GAAIG,MAAM,CAACF,OAAO,CAAC,GAAG,EAAG;MACjD;MACA,OAAOJ,KAAK;IAChB,CAAC,MAAM;MACH;MACA,MAAMG,KAAK,GAAGI,IAAI,CAACC,KAAK,CAACF,MAAM,CAACN,KAAK,CAAC,CAAC;MACvC,MAAMI,OAAO,GAAGG,IAAI,CAACE,KAAK,CAAC,CAACH,MAAM,CAACN,KAAK,CAAC,GAAGG,KAAK,IAAI,EAAE,CAAC;MACxD,OAAO,GAAGA,KAAK,IAAIC,OAAO,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC5D;EACJ,CAAC;EAED,MAAMC,MAAM,GAAGpE,SAAS,CAAC;IACrBqE,aAAa,EAAE;MACXvB,IAAI,GAAAxB,UAAA,GAAEL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,cAAAxB,UAAA,cAAAA,UAAA,GAAI,EAAE;MACtByB,SAAS,EAAE,EAAE;MACbuB,WAAW,EAAE,EAAE;MACfC,OAAO,GAAAhD,aAAA,GAAEN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,OAAO,cAAAhD,aAAA,cAAAA,aAAA,GAAI,EAAE;MAC5BiD,IAAI,GAAAhD,UAAA,GAAEP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuD,IAAI,cAAAhD,UAAA,cAAAA,UAAA,GAAI,EAAE;MACtBiD,OAAO,GAAAhD,aAAA,GAAER,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,OAAO,cAAAhD,aAAA,cAAAA,aAAA,GAAI,EAAE;MAC5BiD,UAAU,GAAAhD,oBAAA,GAAET,IAAI,aAAJA,IAAI,wBAAAU,gBAAA,GAAJV,IAAI,CAAEyD,UAAU,cAAA/C,gBAAA,uBAAhBA,gBAAA,CAAkBgD,GAAG,cAAAjD,oBAAA,cAAAA,oBAAA,GAAI,EAAE;MACvCkD,WAAW,GAAAhD,qBAAA,GAAEX,IAAI,aAAJA,IAAI,wBAAAY,iBAAA,GAAJZ,IAAI,CAAE2D,WAAW,cAAA/C,iBAAA,uBAAjBA,iBAAA,CAAmB8C,GAAG,cAAA/C,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MACzCiD,SAAS,GAAA/C,eAAA,GAAEb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,SAAS,cAAA/C,eAAA,cAAAA,eAAA,GAAI,KAAK,CAAE;IACzC,CAAC;IACDgD,kBAAkB,EAAE,IAAI;IACxBC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAGC,MAAM,IAAK;MAClBC,YAAY,CAACD,MAAM,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF3G,SAAS,CAAC,MAAM;IAAA,IAAA6G,qBAAA;IACZ,MAAMC,IAAI,IAAAD,qBAAA,GAAGf,MAAM,CAACa,MAAM,CAACV,OAAO,cAAAY,qBAAA,uBAArBA,qBAAA,CAAuBpC,SAAS;IAC7C,MAAMsC,KAAK,GAAGjB,MAAM,CAACa,MAAM,CAACI,KAAK;IAEjCjB,MAAM,CAACkB,aAAa,CAAC,WAAW,EAAEF,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,CAAC;IAC7ChB,MAAM,CAACkB,aAAa,CAAC,OAAO,EAAED,KAAK,CAAC;EACxC,CAAC,EAAE,CAACjB,MAAM,CAACa,MAAM,CAACV,OAAO,CAAC,CAAC;EAE3BjG,SAAS,CAAC,MAAM;IACZ,MAAM+G,KAAK,GAAGpE,IAAI,CAACoE,KAAK;IACxB,MAAMd,OAAO,GAAG7B,SAAS,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1C,IAAI,KAAK7B,IAAI,CAACsD,OAAO,CAAC;IAE5D,IAAIA,OAAO,EAAE;MACTH,MAAM,CAACkB,aAAa,CAAC,SAAS,EAAEf,OAAO,CAAC;IAC5C;IAEA,IAAIc,KAAK,IAAId,OAAO,EAAE;MAAA,IAAAkB,YAAA;MAClB,MAAML,IAAI,GAAGb,OAAO,CAACxB,SAAS;MAE9BqB,MAAM,CAACkB,aAAa,CAAC,WAAW,EAAEF,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,CAAC;MAC7ChB,MAAM,CAACkB,aAAa,CAAC,aAAa,EAAED,KAAK,CAACK,SAAS,EAAAD,YAAA,GAACL,IAAI,CAACO,MAAM,cAAAF,YAAA,cAAAA,YAAA,GAAI,CAAC,CAAC,CAAC;IAC1E;EACJ,CAAC,EAAE,CAACxE,IAAI,CAAC,CAAC;EAEV,MAAMiE,YAAY,GAAID,MAAM,IAAK;IAC7B;IACA,MAAMW,gBAAgB,GAAGpD,WAAW,KAAK,SAAS,GAAGyC,MAAM,CAACJ,SAAS,GAAGtB,gBAAgB,CAAC0B,MAAM,CAACJ,SAAS,EAAE,SAAS,CAAC;;IAErH;IACA,MAAMgB,eAAe,GAAGC,UAAU,CAACF,gBAAgB,CAAC;;IAEpD;IACAG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,eAAe,EAAE,OAAOA,eAAe,CAAC;IAE/E,MAAMI,MAAM,GAAG;MACXpD,EAAE,EAAE5B,IAAI,CAAC0D,GAAG;MACZ,GAAGM,MAAM;MACT,GAAG9D,IAAI;MACPkE,KAAK,EAAEJ,MAAM,CAAClC,SAAS,GAAGkC,MAAM,CAACX,WAAW;MAC5CO,SAAS,EAAEgB;IACf,CAAC;IAED9D,QAAQ,CAAChC,WAAW,CAACuC,UAAU,CAAC2D,MAAM,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMC,qBAAqB,GAAIV,CAAC,IAAK;IACjC,MAAM;MAAEhC;IAAM,CAAC,GAAGgC,CAAC,CAACW,MAAM;IAC1B/B,MAAM,CAACkB,aAAa,CAAC,WAAW,EAAE9B,KAAK,CAAC;EAC5C,CAAC;EAED,MAAM4C,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG7D,WAAW,KAAK,SAAS,GAAG,MAAM,GAAG,SAAS;IAChE,MAAM8D,cAAc,GAAG/C,gBAAgB,CAACa,MAAM,CAACa,MAAM,CAACJ,SAAS,EAAEwB,SAAS,CAAC;IAC3E5D,cAAc,CAAC4D,SAAS,CAAC;IACzBjC,MAAM,CAACkB,aAAa,CAAC,WAAW,EAAEgB,cAAc,CAAC;EACrD,CAAC;EAED,oBACIxF,OAAA,CAACpC,IAAI;IAAA6H,QAAA,gBACDzF,OAAA,CAAC/B,UAAU;MAACyH,OAAO,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EAAC;IAAiB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACtEhG,OAAA;MAAMkE,QAAQ,EAAEZ,MAAM,CAACc,YAAa;MAAAqB,QAAA,eAChCzF,OAAA,CAAClC,IAAI;QAACmI,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAT,QAAA,gBACvBzF,OAAA,CAAClC,IAAI;UAACgE,IAAI;UAACqE,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBzF,OAAA,CAACb,KAAK;YAEFkH,KAAK,EAAC,WAAW;YACjBrE,IAAI,EAAC,MAAM;YACXU,KAAK,EAAEY,MAAM,CAACa,MAAM,CAACnC,IAAK;YAC1BsE,QAAQ,EAAEhD,MAAM,CAACiD,YAAa;YAC9BC,KAAK,EAAElD,MAAM,CAACmD,OAAO,CAACzE,IAAI,IAAI0E,OAAO,CAACpD,MAAM,CAACqD,MAAM,CAAC3E,IAAI,CAAE;YAC1D4E,UAAU,EAAEtD,MAAM,CAACmD,OAAO,CAACzE,IAAI,IAAIsB,MAAM,CAACqD,MAAM,CAAC3E;UAAK;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACPhG,OAAA,CAAClC,IAAI;UAACgE,IAAI;UAACqE,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBzF,OAAA,CAACnC,WAAW;YAACgJ,SAAS;YAAApB,QAAA,gBAClBzF,OAAA,CAAC/B,UAAU;cAACyH,OAAO,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClDhG,OAAA,CAACzB,YAAY;cACTuI,aAAa;cAEb9E,IAAI,EAAC,SAAS;cACd+E,OAAO,EAAEnF,SAAU;cACnBc,KAAK,EAAEY,MAAM,CAACa,MAAM,CAACV,OAAQ;cAC7B6C,QAAQ,EAAEA,CAAC5B,CAAC,EAAEsC,GAAG,KAAK;gBAClB1D,MAAM,CAACkB,aAAa,CAAC,SAAS,EAAEwC,GAAG,CAAC;cACxC,CAAE;cACFR,KAAK,EAAElD,MAAM,CAACmD,OAAO,CAAChD,OAAO,IAAIiD,OAAO,CAACpD,MAAM,CAACqD,MAAM,CAAClD,OAAO,CAAE;cAChEmD,UAAU,EAAEtD,MAAM,CAACmD,OAAO,CAAChD,OAAO,IAAIH,MAAM,CAACqD,MAAM,CAAClD,OAAQ;cAC5DwD,cAAc,EAAGC,MAAM;gBAAA,IAAAC,YAAA;gBAAA,QAAAA,YAAA,GAAKD,MAAM,CAAClF,IAAI,cAAAmF,YAAA,cAAAA,YAAA,GAAI,EAAE;cAAA,CAAC;cAC9CC,YAAY,EAAEA,CAAC9G,KAAK,EAAE4G,MAAM,kBACxBlH,OAAA,CAACtC,GAAG;gBAAC2J,SAAS,EAAC,IAAI;gBAAC1B,EAAE,EAAE;kBAAE,SAAS,EAAE;oBAAE2B,EAAE,EAAE,CAAC;oBAAEC,UAAU,EAAE;kBAAE;gBAAE,CAAE;gBAAA,GAAKjH,KAAK;gBAAAmF,QAAA,GACrEyB,MAAM,CAAChF,IAAI,EAAC,GAAC,EAACgF,MAAM,CAAClF,IAAI;cAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CACP;cACFwB,WAAW,EAAGrC,MAAM,iBAAKnF,OAAA,CAACjC,SAAS;gBAAA,GAAKoH,MAAM,CAACsC,UAAU;gBAAA,GAAMtC;cAAM;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACPhG,OAAA,CAAClC,IAAI;UAACgE,IAAI;UAACqE,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBzF,OAAA,CAACnC,WAAW;YAACgJ,SAAS;YAAApB,QAAA,gBAClBzF,OAAA,CAAC/B,UAAU;cAACyH,OAAO,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvDhG,OAAA,CAACtC,GAAG;cAACiI,EAAE,EAAE;gBACL+B,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE;cACT,CAAE;cAAAlC,QAAA,gBACEzF,OAAA,CAACtC,GAAG;gBAACiI,EAAE,EAAE;kBAAEiC,KAAK,EAAE;gBAAG,CAAE;gBAAAnC,QAAA,eACnBzF,OAAA,CAACb,KAAK;kBACFwG,EAAE,EAAE;oBACAkC,SAAS,EAAE,QAAQ;oBACnB,iBAAiB,EAAE;sBACfC,SAAS,EAAE5G,KAAK,CAAC6G,OAAO,CAACC,MAAM,CAACC;oBACpC;kBACJ,CAAE;kBAEFC,YAAY,EAAC,cAAc;kBAC3BlG,IAAI,EAAC,WAAW;kBAChBmG,cAAc,EAAC,GAAG;kBAClB1G,IAAI,EAAC,QAAQ;kBACbiB,KAAK,EAAEY,MAAM,CAACa,MAAM,CAAClC,SAAU;kBAC/BqE,QAAQ,EAAEhD,MAAM,CAACiD;gBAAa;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNhG,OAAA,CAACb,KAAK;gBAEF6C,IAAI,EAAC,aAAa;gBAClBU,KAAK,EAAEY,MAAM,CAACa,MAAM,CAACX,WAAY;gBACjC8C,QAAQ,EAAEhD,MAAM,CAACiD,YAAa;gBAC9BC,KAAK,EAAElD,MAAM,CAACmD,OAAO,CAACjD,WAAW,IAAIkD,OAAO,CAACpD,MAAM,CAACqD,MAAM,CAACnD,WAAW,CAAE;gBACxEoD,UAAU,EAAEtD,MAAM,CAACmD,OAAO,CAACjD,WAAW,IAAIF,MAAM,CAACqD,MAAM,CAACnD;cAAY;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACPhG,OAAA,CAAClC,IAAI;UAACgE,IAAI;UAACqE,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBzF,OAAA,CAACb,KAAK;YACFkH,KAAK,EAAC,MAAM;YACZrE,IAAI,EAAC,MAAM;YACXU,KAAK,EAAEY,MAAM,CAACa,MAAM,CAACT,IAAK;YAC1B4C,QAAQ,EAAEhD,MAAM,CAACiD,YAAa;YAC9BC,KAAK,EAAElD,MAAM,CAACmD,OAAO,CAAC/C,IAAI,IAAIgD,OAAO,CAACpD,MAAM,CAACqD,MAAM,CAACjD,IAAI,CAAE;YAC1DkD,UAAU,EAAEtD,MAAM,CAACmD,OAAO,CAAC/C,IAAI,IAAIJ,MAAM,CAACqD,MAAM,CAACjD;UAAK;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACPhG,OAAA,CAAClC,IAAI;UAACgE,IAAI;UAACqE,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBzF,OAAA,CAACb,KAAK;YACFkH,KAAK,EAAC,SAAS;YACfrE,IAAI,EAAC,SAAS;YACdU,KAAK,EAAEY,MAAM,CAACa,MAAM,CAACR,OAAQ;YAC7B2C,QAAQ,EAAEhD,MAAM,CAACiD,YAAa;YAC9BC,KAAK,EAAElD,MAAM,CAACmD,OAAO,CAAC9C,OAAO,IAAI+C,OAAO,CAACpD,MAAM,CAACqD,MAAM,CAAChD,OAAO,CAAE;YAChEiD,UAAU,EAAEtD,MAAM,CAACmD,OAAO,CAAC9C,OAAO,IAAIL,MAAM,CAACqD,MAAM,CAAChD;UAAQ;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACPhG,OAAA,CAAClC,IAAI;UAACgE,IAAI;UAACqE,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBzF,OAAA,CAACZ,WAAW;YACRiH,KAAK,EAAC,YAAY;YAClBrE,IAAI,EAAC,YAAY;YACjBU,KAAK,EAAEY,MAAM,CAACa,MAAM,CAACP,UAAW;YAChC0C,QAAQ,EAAEhD,MAAM,CAACiD,YAAa;YAC9BC,KAAK,EAAElD,MAAM,CAACmD,OAAO,CAAC7C,UAAU,IAAI8C,OAAO,CAACpD,MAAM,CAACqD,MAAM,CAAC/C,UAAU,CAAE;YACtEgD,UAAU,EAAEtD,MAAM,CAACmD,OAAO,CAAC7C,UAAU,IAAIN,MAAM,CAACqD,MAAM,CAAC/C,UAAW;YAAA6B,QAAA,EAEjEtE,WAAW,CAACU,GAAG,CAAC,CAACC,IAAI,EAAEsG,KAAK,kBACzBpI,OAAA,CAAChC,QAAQ;cAAa0E,KAAK,EAAEZ,IAAI,CAAC+B,GAAI;cAAA4B,QAAA,EACjC3D,IAAI,CAACE;YAAI,GADCoG,KAAK;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACPhG,OAAA,CAAClC,IAAI;UAACgE,IAAI;UAACqE,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBzF,OAAA,CAACZ,WAAW;YACRiH,KAAK,EAAC,aAAa;YACnBrE,IAAI,EAAC,aAAa;YAClBU,KAAK,EAAEY,MAAM,CAACa,MAAM,CAACL,WAAY;YACjCwC,QAAQ,EAAEhD,MAAM,CAACiD,YAAa;YAC9BC,KAAK,EAAElD,MAAM,CAACmD,OAAO,CAAC3C,WAAW,IAAI4C,OAAO,CAACpD,MAAM,CAACqD,MAAM,CAAC7C,WAAW,CAAE;YACxE8C,UAAU,EAAEtD,MAAM,CAACmD,OAAO,CAAC3C,WAAW,IAAIR,MAAM,CAACqD,MAAM,CAAC7C,WAAY;YAAA2B,QAAA,EAEnEpE,YAAY,CAACQ,GAAG,CAAC,CAACC,IAAI,EAAEsG,KAAK,kBAC1BpI,OAAA,CAAChC,QAAQ;cAAa0E,KAAK,EAAEZ,IAAI,CAAC+B,GAAI;cAAA4B,QAAA,EACjC3D,IAAI,CAACE;YAAI,GADCoG,KAAK;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAGPhG,OAAA,CAAClC,IAAI;UAACgE,IAAI;UAACqE,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAX,QAAA,eACrBzF,OAAA,CAACnC,WAAW;YAACgJ,SAAS;YAAApB,QAAA,gBAClBzF,OAAA,CAAC/B,UAAU;cAACyH,OAAO,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3DhG,OAAA,CAACtC,GAAG;cAACiI,EAAE,EAAE;gBACL+B,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,GAAG;gBACRU,UAAU,EAAE;cAChB,CAAE;cAAA5C,QAAA,gBACEzF,OAAA,CAACb,KAAK;gBAEFkH,KAAK,EAAC,EAAE;gBACRrE,IAAI,EAAC,WAAW;gBAChBU,KAAK,EAAEY,MAAM,CAACa,MAAM,CAACJ,SAAU;gBAC/BuC,QAAQ,EAAElB,qBAAsB;gBAChCkD,WAAW,EAAE5G,WAAW,KAAK,SAAS,GAAG,UAAU,GAAG,WAAY;gBAClE8E,KAAK,EAAElD,MAAM,CAACmD,OAAO,CAAC1C,SAAS,IAAI2C,OAAO,CAACpD,MAAM,CAACqD,MAAM,CAAC5C,SAAS,CAAE;gBACpE6C,UAAU,EAAEtD,MAAM,CAACmD,OAAO,CAAC1C,SAAS,IAAIT,MAAM,CAACqD,MAAM,CAAC5C;cAAU;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAEtEhG,OAAA,CAACrC,MAAM;gBACH4K,IAAI,EAAC,OAAO;gBACZ7C,OAAO,EAAC,UAAU;gBAClB8C,OAAO,EAAElD,iBAAkB;gBAAAG,QAAA,EAE1B/D,WAAW,KAAK,SAAS,GAAG,WAAW,GAAG;cAAa;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACNhG,OAAA,CAAC/B,UAAU;cAACyH,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAE8C,EAAE,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAiB,CAAE;cAAAjD,QAAA,EAChE/D,WAAW,KAAK,SAAS,GAAG,qDAAqD,GAAG;YAAqC;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC,eACbhG,OAAA,CAAC/B,UAAU;cAACyH,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAE8C,EAAE,EAAE,CAAC;gBAAEf,OAAO,EAAE,OAAO;gBAAEgB,KAAK,EAAE;cAAY,CAAE;cAAAjD,QAAA,EAAC;YAEnF;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,EAENzG,GAAG,CAACC,OAAO,CAACmJ,OAAO,EAAElJ,QAAQ,CAACU,IAAI,CAAC,iBAChCH,OAAA,CAAClC,IAAI;UAAC6H,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAC3G,IAAI;UAACmE,SAAS;UAAC2C,cAAc,EAAC,UAAU;UAAAnD,QAAA,eACzDzF,OAAA,CAACrC,MAAM;YACH8D,IAAI,EAAC,QAAQ;YACbiH,KAAK,EAAC,SAAS;YACfhD,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAExB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf;AAACzF,EAAA,CAlTuBN,gBAAgB;EAAA,QAEnBxB,WAAW,EACdP,QAAQ,EACFQ,WAAW,EACVA,WAAW,EAChBA,WAAW,EAiDZQ,SAAS;AAAA;AAAA2J,EAAA,GAvDJ5I,gBAAgB;AAAA,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}