{"ast": null, "code": "export const WORK_DAYS = {\n  monday: {\n    name: 'Monday',\n    value: 'monday',\n    short: 'Mon'\n  },\n  tuesday: {\n    name: 'Tuesday',\n    value: 'tuesday',\n    short: 'Tue'\n  },\n  wednesday: {\n    name: 'Wednesday',\n    value: 'wednesday',\n    short: 'Wed'\n  },\n  thursday: {\n    name: 'Thursday',\n    value: 'thursday',\n    short: 'Thu'\n  },\n  friday: {\n    name: 'Friday',\n    value: 'friday',\n    short: 'Fri'\n  },\n  saturday: {\n    name: 'Saturday',\n    value: 'saturday',\n    short: 'Sat'\n  },\n  sunday: {\n    name: 'Sunday',\n    value: 'sunday',\n    short: 'Sun'\n  }\n};\nexport const DEFAULT_WORK_SCHEDULE = {\n  workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],\n  startTime: '09:00',\n  endTime: '17:30',\n  breakDuration: 60,\n  // minutes\n  timezone: 'UTC'\n};\nexport const TIMEZONES = [{\n  value: 'UTC',\n  label: 'UTC (Coordinated Universal Time)'\n}, {\n  value: 'America/New_York',\n  label: 'Eastern Time (ET)'\n}, {\n  value: 'America/Chicago',\n  label: 'Central Time (CT)'\n}, {\n  value: 'America/Denver',\n  label: 'Mountain Time (MT)'\n}, {\n  value: 'America/Los_Angeles',\n  label: 'Pacific Time (PT)'\n}, {\n  value: 'Europe/London',\n  label: 'Greenwich Mean Time (GMT)'\n}, {\n  value: 'Europe/Paris',\n  label: 'Central European Time (CET)'\n}, {\n  value: 'Asia/Tokyo',\n  label: 'Japan Standard Time (JST)'\n}, {\n  value: 'Asia/Shanghai',\n  label: 'China Standard Time (CST)'\n}, {\n  value: 'Asia/Kolkata',\n  label: 'India Standard Time (IST)'\n}, {\n  value: 'Australia/Sydney',\n  label: 'Australian Eastern Time (AET)'\n}];\nexport const TIME_OPTIONS = [];\nfor (let hour = 0; hour < 24; hour++) {\n  for (let minute = 0; minute < 60; minute += 30) {\n    const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;\n    TIME_OPTIONS.push({\n      value: timeString,\n      label: timeString\n    });\n  }\n}\nexport const BREAK_DURATION_OPTIONS = [{\n  value: 30,\n  label: '30 minutes'\n}, {\n  value: 45,\n  label: '45 minutes'\n}, {\n  value: 60,\n  label: '1 hour'\n}, {\n  value: 90,\n  label: '1.5 hours'\n}, {\n  value: 120,\n  label: '2 hours'\n}];", "map": {"version": 3, "names": ["WORK_DAYS", "monday", "name", "value", "short", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday", "DEFAULT_WORK_SCHEDULE", "workDays", "startTime", "endTime", "breakDuration", "timezone", "TIMEZONES", "label", "TIME_OPTIONS", "hour", "minute", "timeString", "toString", "padStart", "push", "BREAK_DURATION_OPTIONS"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/constants/workSchedule.js"], "sourcesContent": ["export const WORK_DAYS = {\n    monday: { name: 'Monday', value: 'monday', short: 'Mon' },\n    tuesday: { name: 'Tuesday', value: 'tuesday', short: 'Tue' },\n    wednesday: { name: 'Wednesday', value: 'wednesday', short: 'Wed' },\n    thursday: { name: 'Thursday', value: 'thursday', short: 'Thu' },\n    friday: { name: 'Friday', value: 'friday', short: 'Fri' },\n    saturday: { name: 'Saturday', value: 'saturday', short: 'Sat' },\n    sunday: { name: 'Sunday', value: 'sunday', short: 'Sun' }\n};\n\nexport const DEFAULT_WORK_SCHEDULE = {\n    workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],\n    startTime: '09:00',\n    endTime: '17:30',\n    breakDuration: 60, // minutes\n    timezone: 'UTC'\n};\n\nexport const TIMEZONES = [\n    { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },\n    { value: 'America/New_York', label: 'Eastern Time (ET)' },\n    { value: 'America/Chicago', label: 'Central Time (CT)' },\n    { value: 'America/Denver', label: 'Mountain Time (MT)' },\n    { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },\n    { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },\n    { value: 'Europe/Paris', label: 'Central European Time (CET)' },\n    { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },\n    { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },\n    { value: 'Asia/Kolkata', label: 'India Standard Time (IST)' },\n    { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' }\n];\n\nexport const TIME_OPTIONS = [];\nfor (let hour = 0; hour < 24; hour++) {\n    for (let minute = 0; minute < 60; minute += 30) {\n        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;\n        TIME_OPTIONS.push({\n            value: timeString,\n            label: timeString\n        });\n    }\n}\n\nexport const BREAK_DURATION_OPTIONS = [\n    { value: 30, label: '30 minutes' },\n    { value: 45, label: '45 minutes' },\n    { value: 60, label: '1 hour' },\n    { value: 90, label: '1.5 hours' },\n    { value: 120, label: '2 hours' }\n];\n"], "mappings": "AAAA,OAAO,MAAMA,SAAS,GAAG;EACrBC,MAAM,EAAE;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAM,CAAC;EACzDC,OAAO,EAAE;IAAEH,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAM,CAAC;EAC5DE,SAAS,EAAE;IAAEJ,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAM,CAAC;EAClEG,QAAQ,EAAE;IAAEL,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAM,CAAC;EAC/DI,MAAM,EAAE;IAAEN,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAM,CAAC;EACzDK,QAAQ,EAAE;IAAEP,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAM,CAAC;EAC/DM,MAAM,EAAE;IAAER,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAM;AAC5D,CAAC;AAED,OAAO,MAAMO,qBAAqB,GAAG;EACjCC,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC;EAClEC,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE,OAAO;EAChBC,aAAa,EAAE,EAAE;EAAE;EACnBC,QAAQ,EAAE;AACd,CAAC;AAED,OAAO,MAAMC,SAAS,GAAG,CACrB;EAAEd,KAAK,EAAE,KAAK;EAAEe,KAAK,EAAE;AAAmC,CAAC,EAC3D;EAAEf,KAAK,EAAE,kBAAkB;EAAEe,KAAK,EAAE;AAAoB,CAAC,EACzD;EAAEf,KAAK,EAAE,iBAAiB;EAAEe,KAAK,EAAE;AAAoB,CAAC,EACxD;EAAEf,KAAK,EAAE,gBAAgB;EAAEe,KAAK,EAAE;AAAqB,CAAC,EACxD;EAAEf,KAAK,EAAE,qBAAqB;EAAEe,KAAK,EAAE;AAAoB,CAAC,EAC5D;EAAEf,KAAK,EAAE,eAAe;EAAEe,KAAK,EAAE;AAA4B,CAAC,EAC9D;EAAEf,KAAK,EAAE,cAAc;EAAEe,KAAK,EAAE;AAA8B,CAAC,EAC/D;EAAEf,KAAK,EAAE,YAAY;EAAEe,KAAK,EAAE;AAA4B,CAAC,EAC3D;EAAEf,KAAK,EAAE,eAAe;EAAEe,KAAK,EAAE;AAA4B,CAAC,EAC9D;EAAEf,KAAK,EAAE,cAAc;EAAEe,KAAK,EAAE;AAA4B,CAAC,EAC7D;EAAEf,KAAK,EAAE,kBAAkB;EAAEe,KAAK,EAAE;AAAgC,CAAC,CACxE;AAED,OAAO,MAAMC,YAAY,GAAG,EAAE;AAC9B,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,EAAE,EAAEA,IAAI,EAAE,EAAE;EAClC,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG,EAAE,EAAEA,MAAM,IAAI,EAAE,EAAE;IAC5C,MAAMC,UAAU,GAAG,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,MAAM,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC9FL,YAAY,CAACM,IAAI,CAAC;MACdtB,KAAK,EAAEmB,UAAU;MACjBJ,KAAK,EAAEI;IACX,CAAC,CAAC;EACN;AACJ;AAEA,OAAO,MAAMI,sBAAsB,GAAG,CAClC;EAAEvB,KAAK,EAAE,EAAE;EAAEe,KAAK,EAAE;AAAa,CAAC,EAClC;EAAEf,KAAK,EAAE,EAAE;EAAEe,KAAK,EAAE;AAAa,CAAC,EAClC;EAAEf,KAAK,EAAE,EAAE;EAAEe,KAAK,EAAE;AAAS,CAAC,EAC9B;EAAEf,KAAK,EAAE,EAAE;EAAEe,KAAK,EAAE;AAAY,CAAC,EACjC;EAAEf,KAAK,EAAE,GAAG;EAAEe,KAAK,EAAE;AAAU,CAAC,CACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}