[{"E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\index.js": "1", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\reportWebVitals.js": "2", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\store.js": "3", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\routes.js": "4", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\theme.js": "5", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\reducers.js": "6", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\index.js": "7", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Backgroundprovider.jsx": "8", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Create.js": "9", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Form.js": "10", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\MainLayout.js": "11", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\AuthLayout.js": "12", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\Form.js": "13", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\Form.js": "14", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Auth\\Login.js": "15", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\Form.js": "16", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\Form.js": "17", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PermissionRoute.js": "18", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveApproval\\Approval.jsx": "19", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\Form.js": "20", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\config\\permissionConfig.js": "21", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\permission.js": "22", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveConfiguration\\LeaveConfiguration.jsx": "23", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveReport\\LeaveReport.jsx": "24", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Tasklist.jsx": "25", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveCalendar\\LeaveCalendar.jsx": "26", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductList.jsx": "27", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TaskHistoryAdmin.jsx": "28", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductTimesheet.jsx": "29", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\NotFound.jsx": "30", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductOverview.jsx": "31", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\AccessDenied.jsx": "32", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Client\\Client.jsx": "33", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\ProductListStaff.jsx": "34", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\TaskListWithNote.jsx": "35", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\GeneralSlice.js": "36", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AuthSlice.js": "37", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\UserSlice.js": "38", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AttendanceSlice.js": "39", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DesignationSlice.js": "40", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DepartmentSlice.js": "41", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ExpensesSlice.js": "42", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\LeaveSlice.js": "43", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\TimelineSlice.js": "44", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ProductSlice.js": "45", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ClientSlice.js": "46", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SprintSlice.js": "47", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SettingSlice.js": "48", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\UserSprintPage.jsx": "49", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintTasksPage.jsx": "50", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintPage.jsx": "51", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\index.js": "52", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\index.js": "53", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\index.js": "54", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\index.js": "55", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\index.js": "56", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\index.js": "57", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\index.js": "58", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\index.js": "59", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AuthSaga.js": "60", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\UserSaga.js": "61", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DepartmentSaga.js": "62", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DesignationSaga.js": "63", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AttendanceSaga.js": "64", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ExpenseSaga.js": "65", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\LeaveSaga.js": "66", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\TimelineSaga.js": "67", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ActivitySaga.js": "68", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ProductSaga.js": "69", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ClientSaga.js": "70", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SprintSaga.js": "71", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SettingSaga.js": "72", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\index.js": "73", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Setting\\index.js": "74", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\index.js": "75", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\index.jsx": "76", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\actions.js": "77", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PageTitle.js": "78", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\role.js": "79", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Permission.jsx": "80", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Input.js": "81", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\SelectField.js": "82", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\constants\\menus.js": "83", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\menuGenerator.js": "84", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\permissionLogger.js": "85", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\can.js": "86", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\FormSkeleton.js": "87", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\sort.js": "88", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\leaveConst.js": "89", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SettingSelector.js": "90", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\TimelineSelector.js": "91", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\FloatingButton.js": "92", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\AccountSetting.js": "93", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\BasicInformation.js": "94", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\MenuForm.js": "95", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductAdd.jsx": "96", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SprintSelector.js": "97", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TimesheetFilters.jsx": "98", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\WeeklyPicker.jsx": "99", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\DateRangeSelector.jsx": "100", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ProductSelector.js": "101", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\CustomMenu.js": "102", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\ListSkeleton.js": "103", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Charts.js": "104", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widgets.js": "105", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\DialogConfirm.js": "106", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\convertion.js": "107", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\expenseStatus.js": "108", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\UserLeaveInfo.jsx": "109", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\LeaveCalender.jsx": "110", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Filter.js": "111", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskFilterUser.jsx": "112", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\AddMembers.jsx": "113", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskHeader.jsx": "114", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeader.jsx": "115", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskInfoComponent.jsx": "116", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeaderFilterStaff.jsx": "117", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\Note.jsx": "118", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintList.jsx": "119", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintForm.jsx": "120", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\GeneralSelector.js": "121", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\UserSelector.js": "122", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DesignationSelector.js": "123", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AuthSelector.js": "124", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DepartmentSelector.js": "125", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ExpensesSelector.js": "126", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AttendanceSelector.js": "127", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ClientSelector.js": "128", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\LeaveSelector.js": "129", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\countries.js": "130", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\TimelineNew.jsx": "131", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SettingService.js": "132", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SprintService.js": "133", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\TimelineService.js": "134", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ActivityService.js": "135", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ProductService.js": "136", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\EmployeeList.js": "137", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\AttendanceList.js": "138", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\AccountSetting.js": "139", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\ExpensesList.js": "140", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\BasicInformation.js": "141", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\index.js": "142", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\apiConfig.js": "143", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\BasicInformation.js": "144", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Leave.js": "145", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Attendance.js": "146", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\AccountSetting.js": "147", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\constant\\ProductConts.js": "148", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widget.js": "149", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Activity.js": "150", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\FormLeavePop.jsx": "151", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\DayPicker.jsx": "152", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\MonthPicker.jsx": "153", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\api.js": "154", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AuthService.js": "155", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DepartmentService.js": "156", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\UserService.js": "157", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AttendanceService.js": "158", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DesignationService.js": "159", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ExpensesService.js": "160", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\LeaveService.js": "161", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\gender.js": "162", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ClientService.js": "163", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\WeekView.jsx": "164", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\MonthView.jsx": "165", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\DayView.jsx": "166", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TodayGoal.jsx": "167", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\BreakReasone.jsx": "168", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\EarlyLate.jsx": "169", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\AttendanceBarChart.jsx": "170", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\ProductivityChart.jsx": "171", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\WorkHoursStatus.jsx": "172", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\OverLimitBreak.jsx": "173", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TimelineRequest.jsx": "174", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\App.js": "175", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ActivitySlice.js": "176", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ActivitySelector.js": "177", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\LoadingScreen.js": "178", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\WorkSchedule.jsx": "179", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TaskRequest\\TaskRequest.jsx": "180", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TimeRequest\\TimeRequest.jsx": "181", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\Overview.jsx": "182", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\AdminDashboard.js": "183", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\UserDashboard.js": "184", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\DayPicker.jsx": "185", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\WeeklyPicker.jsx": "186", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\MonthPicker.jsx": "187", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\MonthlyWorkReport.jsx": "188", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\DayWorkReport.jsx": "189", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\WeekWorkReport.jsx": "190", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\MonthPicker.jsx": "191", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\DayPicker.jsx": "192", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkSchedule.jsx": "193", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\WeekWorkSchedule.jsx": "194", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkSchedule.jsx": "195", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\WeeklyPicker.jsx": "196", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\workSchedule.js": "197", "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\WorkScheduleService.js": "198"}, {"size": 850, "mtime": 1749628571403, "results": "199", "hashOfConfig": "200"}, {"size": 375, "mtime": 1747124732125, "results": "201", "hashOfConfig": "200"}, {"size": 1749, "mtime": 1748931717673, "results": "202", "hashOfConfig": "200"}, {"size": 16086, "mtime": 1749883708571, "results": "203", "hashOfConfig": "200"}, {"size": 3742, "mtime": 1747124732306, "results": "204", "hashOfConfig": "200"}, {"size": 2423, "mtime": 1748931717657, "results": "205", "hashOfConfig": "200"}, {"size": 1813, "mtime": 1748931717579, "results": "206", "hashOfConfig": "200"}, {"size": 8531, "mtime": 1748931673699, "results": "207", "hashOfConfig": "200"}, {"size": 5702, "mtime": 1749628571544, "results": "208", "hashOfConfig": "200"}, {"size": 4922, "mtime": 1747124732253, "results": "209", "hashOfConfig": "200"}, {"size": 11847, "mtime": 1748931717566, "results": "210", "hashOfConfig": "200"}, {"size": 1496, "mtime": 1748407262947, "results": "211", "hashOfConfig": "200"}, {"size": 4099, "mtime": 1748522688462, "results": "212", "hashOfConfig": "200"}, {"size": 4448, "mtime": 1748522688462, "results": "213", "hashOfConfig": "200"}, {"size": 6835, "mtime": 1748931700850, "results": "214", "hashOfConfig": "200"}, {"size": 5567, "mtime": 1747124732157, "results": "215", "hashOfConfig": "200"}, {"size": 8416, "mtime": 1748522688462, "results": "216", "hashOfConfig": "200"}, {"size": 3309, "mtime": 1748931717558, "results": "217", "hashOfConfig": "200"}, {"size": 5376, "mtime": 1748577220729, "results": "218", "hashOfConfig": "200"}, {"size": 9948, "mtime": 1748522688462, "results": "219", "hashOfConfig": "200"}, {"size": 15791, "mtime": 1748931717559, "results": "220", "hashOfConfig": "200"}, {"size": 1418, "mtime": 1748931717560, "results": "221", "hashOfConfig": "200"}, {"size": 17087, "mtime": 1748577220731, "results": "222", "hashOfConfig": "200"}, {"size": 13496, "mtime": 1748577220732, "results": "223", "hashOfConfig": "200"}, {"size": 15169, "mtime": 1748931717604, "results": "224", "hashOfConfig": "200"}, {"size": 6020, "mtime": 1748577220730, "results": "225", "hashOfConfig": "200"}, {"size": 5081, "mtime": 1748931700862, "results": "226", "hashOfConfig": "200"}, {"size": 7879, "mtime": 1748931700867, "results": "227", "hashOfConfig": "200"}, {"size": 12838, "mtime": 1748931673717, "results": "228", "hashOfConfig": "200"}, {"size": 2170, "mtime": 1748931717558, "results": "229", "hashOfConfig": "200"}, {"size": 34700, "mtime": 1748931700864, "results": "230", "hashOfConfig": "200"}, {"size": 2936, "mtime": 1748931717557, "results": "231", "hashOfConfig": "200"}, {"size": 8267, "mtime": 1748931700851, "results": "232", "hashOfConfig": "200"}, {"size": 4588, "mtime": 1748931700865, "results": "233", "hashOfConfig": "200"}, {"size": 8098, "mtime": 1748931700866, "results": "234", "hashOfConfig": "200"}, {"size": 1503, "mtime": 1748577220768, "results": "235", "hashOfConfig": "200"}, {"size": 520, "mtime": 1748406924449, "results": "236", "hashOfConfig": "200"}, {"size": 1051, "mtime": 1748406962467, "results": "237", "hashOfConfig": "200"}, {"size": 1006, "mtime": 1748942595099, "results": "238", "hashOfConfig": "200"}, {"size": 819, "mtime": 1748406934175, "results": "239", "hashOfConfig": "200"}, {"size": 804, "mtime": 1748406931614, "results": "240", "hashOfConfig": "200"}, {"size": 759, "mtime": 1748406938656, "results": "241", "hashOfConfig": "200"}, {"size": 762, "mtime": 1748406944863, "results": "242", "hashOfConfig": "200"}, {"size": 790, "mtime": 1748931700917, "results": "243", "hashOfConfig": "200"}, {"size": 1540, "mtime": 1748931717671, "results": "244", "hashOfConfig": "200"}, {"size": 822, "mtime": 1748931700911, "results": "245", "hashOfConfig": "200"}, {"size": 2502, "mtime": 1748931717672, "results": "246", "hashOfConfig": "200"}, {"size": 711, "mtime": 1748931717671, "results": "247", "hashOfConfig": "200"}, {"size": 3648, "mtime": 1748931717617, "results": "248", "hashOfConfig": "200"}, {"size": 10309, "mtime": 1748931717617, "results": "249", "hashOfConfig": "200"}, {"size": 5830, "mtime": 1748931717616, "results": "250", "hashOfConfig": "200"}, {"size": 14290, "mtime": 1748931700849, "results": "251", "hashOfConfig": "200"}, {"size": 2903, "mtime": 1749883647002, "results": "252", "hashOfConfig": "200"}, {"size": 8680, "mtime": 1747124732175, "results": "253", "hashOfConfig": "200"}, {"size": 8383, "mtime": 1748931717587, "results": "254", "hashOfConfig": "200"}, {"size": 10366, "mtime": 1748931673713, "results": "255", "hashOfConfig": "200"}, {"size": 9103, "mtime": 1748931700889, "results": "256", "hashOfConfig": "200"}, {"size": 7977, "mtime": 1748931717586, "results": "257", "hashOfConfig": "200"}, {"size": 428, "mtime": 1748931717639, "results": "258", "hashOfConfig": "200"}, {"size": 1744, "mtime": 1748931700840, "results": "259", "hashOfConfig": "200"}, {"size": 5371, "mtime": 1748931673690, "results": "260", "hashOfConfig": "200"}, {"size": 4605, "mtime": 1748931717572, "results": "261", "hashOfConfig": "200"}, {"size": 3891, "mtime": 1748407155402, "results": "262", "hashOfConfig": "200"}, {"size": 7136, "mtime": 1749881113570, "results": "263", "hashOfConfig": "200"}, {"size": 3839, "mtime": 1748407157768, "results": "264", "hashOfConfig": "200"}, {"size": 4440, "mtime": 1748577220703, "results": "265", "hashOfConfig": "200"}, {"size": 3614, "mtime": 1748931700844, "results": "266", "hashOfConfig": "200"}, {"size": 12083, "mtime": 1749883708572, "results": "267", "hashOfConfig": "200"}, {"size": 14918, "mtime": 1748931717573, "results": "268", "hashOfConfig": "200"}, {"size": 3509, "mtime": 1748931700840, "results": "269", "hashOfConfig": "200"}, {"size": 3545, "mtime": 1748931717575, "results": "270", "hashOfConfig": "200"}, {"size": 4806, "mtime": 1748931717574, "results": "271", "hashOfConfig": "200"}, {"size": 1767, "mtime": 1747124732210, "results": "272", "hashOfConfig": "200"}, {"size": 14741, "mtime": 1748931717610, "results": "273", "hashOfConfig": "200"}, {"size": 6154, "mtime": 1748931700872, "results": "274", "hashOfConfig": "200"}, {"size": 157, "mtime": 1748931717634, "results": "275", "hashOfConfig": "200"}, {"size": 2035, "mtime": 1748931717656, "results": "276", "hashOfConfig": "200"}, {"size": 982, "mtime": 1747124732082, "results": "277", "hashOfConfig": "200"}, {"size": 421, "mtime": 1747124732095, "results": "278", "hashOfConfig": "200"}, {"size": 31332, "mtime": 1749620217948, "results": "279", "hashOfConfig": "200"}, {"size": 810, "mtime": 1748577220695, "results": "280", "hashOfConfig": "200"}, {"size": 691, "mtime": 1747124732082, "results": "281", "hashOfConfig": "200"}, {"size": 1147, "mtime": 1748931700888, "results": "282", "hashOfConfig": "200"}, {"size": 11441, "mtime": 1748931717676, "results": "283", "hashOfConfig": "200"}, {"size": 1998, "mtime": 1748931717676, "results": "284", "hashOfConfig": "200"}, {"size": 9000, "mtime": 1748931717675, "results": "285", "hashOfConfig": "200"}, {"size": 852, "mtime": 1748412455335, "results": "286", "hashOfConfig": "200"}, {"size": 538, "mtime": 1747124732095, "results": "287", "hashOfConfig": "200"}, {"size": 887, "mtime": 1747124732095, "results": "288", "hashOfConfig": "200"}, {"size": 258, "mtime": 1748407111255, "results": "289", "hashOfConfig": "200"}, {"size": 418, "mtime": 1748931700894, "results": "290", "hashOfConfig": "200"}, {"size": 559, "mtime": 1747124732082, "results": "291", "hashOfConfig": "200"}, {"size": 4324, "mtime": 1748522688026, "results": "292", "hashOfConfig": "200"}, {"size": 8047, "mtime": 1748522688023, "results": "293", "hashOfConfig": "200"}, {"size": 1478, "mtime": 1747124732268, "results": "294", "hashOfConfig": "200"}, {"size": 13604, "mtime": 1748931717588, "results": "295", "hashOfConfig": "200"}, {"size": 1176, "mtime": 1748931717639, "results": "296", "hashOfConfig": "200"}, {"size": 6388, "mtime": 1748931673725, "results": "297", "hashOfConfig": "200"}, {"size": 7340, "mtime": 1748931673725, "results": "298", "hashOfConfig": "200"}, {"size": 5652, "mtime": 1748931673714, "results": "299", "hashOfConfig": "200"}, {"size": 765, "mtime": 1748931700892, "results": "300", "hashOfConfig": "200"}, {"size": 2107, "mtime": 1747124732082, "results": "301", "hashOfConfig": "200"}, {"size": 442, "mtime": 1747124732082, "results": "302", "hashOfConfig": "200"}, {"size": 2293, "mtime": 1747124732157, "results": "303", "hashOfConfig": "200"}, {"size": 4375, "mtime": 1749628571517, "results": "304", "hashOfConfig": "200"}, {"size": 1066, "mtime": 1747124732082, "results": "305", "hashOfConfig": "200"}, {"size": 1389, "mtime": 1748931717675, "results": "306", "hashOfConfig": "200"}, {"size": 103, "mtime": 1747124732095, "results": "307", "hashOfConfig": "200"}, {"size": 5107, "mtime": 1749628700868, "results": "308", "hashOfConfig": "200"}, {"size": 11215, "mtime": 1748931673712, "results": "309", "hashOfConfig": "200"}, {"size": 5279, "mtime": 1747124732263, "results": "310", "hashOfConfig": "200"}, {"size": 6649, "mtime": 1748931700869, "results": "311", "hashOfConfig": "200"}, {"size": 4086, "mtime": 1748931673727, "results": "312", "hashOfConfig": "200"}, {"size": 8457, "mtime": 1748931717607, "results": "313", "hashOfConfig": "200"}, {"size": 2827, "mtime": 1748931673728, "results": "314", "hashOfConfig": "200"}, {"size": 7765, "mtime": 1748931700871, "results": "315", "hashOfConfig": "200"}, {"size": 4366, "mtime": 1748931673729, "results": "316", "hashOfConfig": "200"}, {"size": 276, "mtime": 1748931673728, "results": "317", "hashOfConfig": "200"}, {"size": 8077, "mtime": 1748931717614, "results": "318", "hashOfConfig": "200"}, {"size": 8730, "mtime": 1748931717611, "results": "319", "hashOfConfig": "200"}, {"size": 935, "mtime": 1748931717638, "results": "320", "hashOfConfig": "200"}, {"size": 1299, "mtime": 1748407122979, "results": "321", "hashOfConfig": "200"}, {"size": 1420, "mtime": 1748407093178, "results": "322", "hashOfConfig": "200"}, {"size": 223, "mtime": 1748407072019, "results": "323", "hashOfConfig": "200"}, {"size": 1388, "mtime": 1748407090945, "results": "324", "hashOfConfig": "200"}, {"size": 1320, "mtime": 1748407095953, "results": "325", "hashOfConfig": "200"}, {"size": 570, "mtime": 1748407067480, "results": "326", "hashOfConfig": "200"}, {"size": 377, "mtime": 1748407074575, "results": "327", "hashOfConfig": "200"}, {"size": 720, "mtime": 1748407105341, "results": "328", "hashOfConfig": "200"}, {"size": 39922, "mtime": 1747124732095, "results": "329", "hashOfConfig": "200"}, {"size": 19835, "mtime": 1749883708580, "results": "330", "hashOfConfig": "200"}, {"size": 1214, "mtime": 1748931717652, "results": "331", "hashOfConfig": "200"}, {"size": 6427, "mtime": 1749883708585, "results": "332", "hashOfConfig": "200"}, {"size": 1904, "mtime": 1749883708585, "results": "333", "hashOfConfig": "200"}, {"size": 6225, "mtime": 1749883708581, "results": "334", "hashOfConfig": "200"}, {"size": 11619, "mtime": 1749883708584, "results": "335", "hashOfConfig": "200"}, {"size": 9695, "mtime": 1749883647020, "results": "336", "hashOfConfig": "200"}, {"size": 7851, "mtime": 1748931700873, "results": "337", "hashOfConfig": "200"}, {"size": 2762, "mtime": 1748522688462, "results": "338", "hashOfConfig": "200"}, {"size": 7115, "mtime": 1747124732210, "results": "339", "hashOfConfig": "200"}, {"size": 6657, "mtime": 1748522688462, "results": "340", "hashOfConfig": "200"}, {"size": 425, "mtime": 1749892531655, "results": "341", "hashOfConfig": "200"}, {"size": 1292, "mtime": 1749883708587, "results": "342", "hashOfConfig": "200"}, {"size": 19207, "mtime": 1749892426155, "results": "343", "hashOfConfig": "200"}, {"size": 3443, "mtime": 1747124732268, "results": "344", "hashOfConfig": "200"}, {"size": 4977, "mtime": 1747124732263, "results": "345", "hashOfConfig": "200"}, {"size": 6426, "mtime": 1748522688413, "results": "346", "hashOfConfig": "200"}, {"size": 207, "mtime": 1748931717608, "results": "347", "hashOfConfig": "200"}, {"size": 853, "mtime": 1747124732167, "results": "348", "hashOfConfig": "200"}, {"size": 25088, "mtime": 1749883708579, "results": "349", "hashOfConfig": "200"}, {"size": 13333, "mtime": 1748577220725, "results": "350", "hashOfConfig": "200"}, {"size": 13294, "mtime": 1748931717619, "results": "351", "hashOfConfig": "200"}, {"size": 4868, "mtime": 1748931717620, "results": "352", "hashOfConfig": "200"}, {"size": 5510, "mtime": 1748931717674, "results": "353", "hashOfConfig": "200"}, {"size": 2025, "mtime": 1748931717642, "results": "354", "hashOfConfig": "200"}, {"size": 739, "mtime": 1748931717644, "results": "355", "hashOfConfig": "200"}, {"size": 3197, "mtime": 1749883708586, "results": "356", "hashOfConfig": "200"}, {"size": 1620, "mtime": 1749883708581, "results": "357", "hashOfConfig": "200"}, {"size": 693, "mtime": 1749883708582, "results": "358", "hashOfConfig": "200"}, {"size": 634, "mtime": 1749883708583, "results": "359", "hashOfConfig": "200"}, {"size": 675, "mtime": 1749883708583, "results": "360", "hashOfConfig": "200"}, {"size": 185, "mtime": 1747124732095, "results": "361", "hashOfConfig": "200"}, {"size": 2375, "mtime": 1749883708582, "results": "362", "hashOfConfig": "200"}, {"size": 19078, "mtime": 1749883647036, "results": "363", "hashOfConfig": "200"}, {"size": 15339, "mtime": 1748931717623, "results": "364", "hashOfConfig": "200"}, {"size": 20031, "mtime": 1748931717622, "results": "365", "hashOfConfig": "200"}, {"size": 5352, "mtime": 1749628571486, "results": "366", "hashOfConfig": "200"}, {"size": 2880, "mtime": 1749628571441, "results": "367", "hashOfConfig": "200"}, {"size": 4065, "mtime": 1749628571458, "results": "368", "hashOfConfig": "200"}, {"size": 3060, "mtime": 1748931717582, "results": "369", "hashOfConfig": "200"}, {"size": 27946, "mtime": 1749883646989, "results": "370", "hashOfConfig": "200"}, {"size": 6582, "mtime": 1748931717585, "results": "371", "hashOfConfig": "200"}, {"size": 5723, "mtime": 1749628571471, "results": "372", "hashOfConfig": "200"}, {"size": 8635, "mtime": 1748931673707, "results": "373", "hashOfConfig": "200"}, {"size": 1364, "mtime": 1749883646955, "results": "374", "hashOfConfig": "200"}, {"size": 1849, "mtime": 1749883708586, "results": "375", "hashOfConfig": "200"}, {"size": 272, "mtime": 1749714907747, "results": "376", "hashOfConfig": "200"}, {"size": 532, "mtime": 1749883646956, "results": "377", "hashOfConfig": "200"}, {"size": 4253, "mtime": 1749890030051, "results": "378", "hashOfConfig": "200"}, {"size": 131, "mtime": 1749883708577, "results": "379", "hashOfConfig": "200"}, {"size": 5121, "mtime": 1749883708577, "results": "380", "hashOfConfig": "200"}, {"size": 5407, "mtime": 1749883708572, "results": "381", "hashOfConfig": "200"}, {"size": 4120, "mtime": 1749883646964, "results": "382", "hashOfConfig": "200"}, {"size": 1576, "mtime": 1749883646964, "results": "383", "hashOfConfig": "200"}, {"size": 12041, "mtime": 1749883708573, "results": "384", "hashOfConfig": "200"}, {"size": 8608, "mtime": 1749883708574, "results": "385", "hashOfConfig": "200"}, {"size": 6003, "mtime": 1749883708574, "results": "386", "hashOfConfig": "200"}, {"size": 5938, "mtime": 1749883708576, "results": "387", "hashOfConfig": "200"}, {"size": 6755, "mtime": 1749883708575, "results": "388", "hashOfConfig": "200"}, {"size": 5805, "mtime": 1749883708576, "results": "389", "hashOfConfig": "200"}, {"size": 6003, "mtime": 1749883708574, "results": "390", "hashOfConfig": "200"}, {"size": 12041, "mtime": 1749883708573, "results": "391", "hashOfConfig": "200"}, {"size": 143, "mtime": 1749888612424, "results": "392", "hashOfConfig": "200"}, {"size": 146, "mtime": 1749888613859, "results": "393", "hashOfConfig": "200"}, {"size": 149, "mtime": 1749888619684, "results": "394", "hashOfConfig": "200"}, {"size": 8608, "mtime": 1749889937971, "results": "395", "hashOfConfig": "200"}, {"size": 683, "mtime": 1749892082794, "results": "396", "hashOfConfig": "200"}, {"size": 3067, "mtime": 1749892550396, "results": "397", "hashOfConfig": "200"}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "utrof9", {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\reportWebVitals.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\store.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\routes.js", ["992"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\theme.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\reducers.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Backgroundprovider.jsx", ["993", "994"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Create.js", ["995"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\MainLayout.js", ["996", "997"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\layouts\\AuthLayout.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Auth\\Login.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PermissionRoute.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveApproval\\Approval.jsx", ["998", "999"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\Form.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\config\\permissionConfig.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\permission.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveConfiguration\\LeaveConfiguration.jsx", ["1000", "1001"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveReport\\LeaveReport.jsx", ["1002"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Tasklist.jsx", ["1003"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\LeaveCalendar\\LeaveCalendar.jsx", ["1004"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductList.jsx", ["1005"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TaskHistoryAdmin.jsx", ["1006"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductTimesheet.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\NotFound.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductOverview.jsx", ["1007"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\AccessDenied.jsx", ["1008"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Client\\Client.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\ProductListStaff.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\Staff\\TaskListWithNote.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\GeneralSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AuthSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\UserSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\AttendanceSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DesignationSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\DepartmentSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ExpensesSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\LeaveSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\TimelineSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ProductSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ClientSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SprintSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\SettingSlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\UserSprintPage.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintTasksPage.jsx", ["1009", "1010"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\pages\\SprintPage.jsx", ["1011", "1012"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Attendance\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Designation\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Expenses\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\index.js", ["1013"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Department\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AuthSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\UserSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DepartmentSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\DesignationSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\AttendanceSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ExpenseSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\LeaveSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\TimelineSaga.js", ["1014"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ActivitySaga.js", ["1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ProductSaga.js", ["1025", "1026"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\ClientSaga.js", ["1027", "1028", "1029"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SprintSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\sagas\\SettingSaga.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Setting\\index.js", ["1030"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\index.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\actions.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\PageTitle.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\role.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\Permission.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Input.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\SelectField.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\constants\\menus.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\menuGenerator.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\permissionLogger.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\can.js", ["1031"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\FormSkeleton.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\sort.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\leaveConst.js", ["1032"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SettingSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\TimelineSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\FloatingButton.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\AccountSetting.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Create\\BasicInformation.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\MenuForm.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\ProductAdd.jsx", ["1033"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\SprintSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\TimesheetFilters.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\WeeklyPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\DateRangeSelector.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ProductSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\CustomMenu.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\Skeleton\\ListSkeleton.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Charts.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widgets.js", ["1034", "1035", "1036", "1037", "1038"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\DialogConfirm.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\convertion.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\expenseStatus.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\UserLeaveInfo.jsx", ["1039"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\LeaveCalender.jsx", ["1040", "1041"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Filter.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskFilterUser.jsx", ["1042"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\AddMembers.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskHeader.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeader.jsx", ["1043", "1044"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\TaskInfoComponent.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\ProductHeaderFilterStaff.jsx", ["1045"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\components\\Note.jsx", ["1046"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintList.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Sprints\\components\\SprintForm.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\GeneralSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\UserSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DesignationSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AuthSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\DepartmentSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ExpensesSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\AttendanceSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ClientSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\LeaveSelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\countries.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\TimelineNew.jsx", ["1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SettingService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\SprintService.js", ["1063", "1064"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\TimelineService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ActivityService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ProductService.js", ["1065"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\EmployeeList.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\AttendanceList.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\AccountSetting.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Report\\components\\ExpensesList.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Profile\\components\\BasicInformation.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\index.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\apiConfig.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\BasicInformation.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Leave.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\Attendance.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\User\\components\\Form\\AccountSetting.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Product\\constant\\ProductConts.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Widget.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\Activity.js", ["1066", "1067"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Leave\\FormLeavePop.jsx", ["1068", "1069", "1070"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\DayPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\MonthPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\utils\\api.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AuthService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DepartmentService.js", ["1071"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\UserService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\AttendanceService.js", ["1072"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\DesignationService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ExpensesService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\LeaveService.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\gender.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\ClientService.js", ["1073"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\WeekView.jsx", ["1074", "1075", "1076", "1077"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\MonthView.jsx", ["1078", "1079", "1080", "1081"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Timeline\\components\\DayView.jsx", ["1082", "1083", "1084"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TodayGoal.jsx", ["1085", "1086", "1087", "1088"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\BreakReasone.jsx", ["1089", "1090"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\EarlyLate.jsx", ["1091", "1092"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\AttendanceBarChart.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\ProductivityChart.jsx", ["1093", "1094", "1095", "1096", "1097"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\WorkHoursStatus.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\OverLimitBreak.jsx", ["1098"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\components\\TimelineRequest.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\App.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\slices\\slice\\ActivitySlice.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\selectors\\ActivitySelector.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\components\\LoadingScreen.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\WorkSchedule.jsx", ["1099", "1100", "1101"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TaskRequest\\TaskRequest.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\TimeRequest\\TimeRequest.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\Overview.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\AdminDashboard.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\Dashboard\\UserDashboard.js", ["1102"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\DayPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\WeeklyPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\TimePickers\\MonthPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\MonthlyWorkReport.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\DayWorkReport.jsx", ["1103"], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\Overview\\component\\WeekWorkReport.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\MonthPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\DayPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\DayWorkSchedule.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\WeekWorkSchedule.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\Components\\MonthWorkSchedule.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\screens\\ActivityTimeline\\WorkSchedule\\TimePickers\\WeeklyPicker.jsx", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\constants\\workSchedule.js", [], [], "E:\\Suraj Patil\\Organization_Management_System\\meraki-frontend-main\\src\\services\\WorkScheduleService.js", [], [], {"ruleId": "1104", "severity": 1, "message": "1105", "line": 390, "column": 12, "nodeType": "1106", "messageId": "1107", "endLine": 390, "endColumn": 20}, {"ruleId": "1104", "severity": 1, "message": "1108", "line": 1, "column": 21, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 28}, {"ruleId": "1104", "severity": 1, "message": "1109", "line": 18, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 18, "endColumn": 24}, {"ruleId": "1104", "severity": 1, "message": "1110", "line": 21, "column": 8, "nodeType": "1106", "messageId": "1107", "endLine": 21, "endColumn": 18}, {"ruleId": "1104", "severity": 1, "message": "1111", "line": 31, "column": 8, "nodeType": "1106", "messageId": "1107", "endLine": 31, "endColumn": 11}, {"ruleId": "1104", "severity": 1, "message": "1112", "line": 36, "column": 8, "nodeType": "1106", "messageId": "1107", "endLine": 36, "endColumn": 13}, {"ruleId": "1104", "severity": 1, "message": "1113", "line": 14, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 14, "endColumn": 7}, {"ruleId": "1104", "severity": 1, "message": "1114", "line": 40, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 40, "endColumn": 14}, {"ruleId": "1104", "severity": 1, "message": "1113", "line": 14, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 14, "endColumn": 7}, {"ruleId": "1104", "severity": 1, "message": "1115", "line": 16, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 16, "endColumn": 20}, {"ruleId": "1104", "severity": 1, "message": "1116", "line": 66, "column": 8, "nodeType": "1106", "messageId": "1107", "endLine": 66, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1117", "line": 15, "column": 7, "nodeType": "1106", "messageId": "1107", "endLine": 15, "endColumn": 16}, {"ruleId": "1104", "severity": 1, "message": "1108", "line": 9, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 9, "endColumn": 17}, {"ruleId": "1104", "severity": 1, "message": "1117", "line": 27, "column": 7, "nodeType": "1106", "messageId": "1107", "endLine": 27, "endColumn": 16}, {"ruleId": "1104", "severity": 1, "message": "1118", "line": 46, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 46, "endColumn": 16}, {"ruleId": "1104", "severity": 1, "message": "1119", "line": 204, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 204, "endColumn": 20}, {"ruleId": "1104", "severity": 1, "message": "1120", "line": 5, "column": 8, "nodeType": "1106", "messageId": "1107", "endLine": 5, "endColumn": 24}, {"ruleId": "1104", "severity": 1, "message": "1121", "line": 22, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 22, "endColumn": 10}, {"ruleId": "1104", "severity": 1, "message": "1122", "line": 36, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 36, "endColumn": 20}, {"ruleId": "1104", "severity": 1, "message": "1123", "line": 34, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 34, "endColumn": 17}, {"ruleId": "1104", "severity": 1, "message": "1124", "line": 131, "column": 7, "nodeType": "1106", "messageId": "1107", "endLine": 131, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1125", "line": 50, "column": 11, "nodeType": "1106", "messageId": "1107", "endLine": 50, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1126", "line": 11, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 11, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1126", "line": 11, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 11, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1127", "line": 95, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 95, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1127", "line": 117, "column": 13, "nodeType": "1106", "messageId": "1107", "endLine": 117, "endColumn": 17}, {"ruleId": "1104", "severity": 1, "message": "1127", "line": 143, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 143, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1127", "line": 165, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 165, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1127", "line": 186, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 186, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1126", "line": 206, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 206, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1127", "line": 227, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 227, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1127", "line": 248, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 248, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1127", "line": 269, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 269, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1126", "line": 45, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 45, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1127", "line": 92, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 92, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1126", "line": 12, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 12, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1126", "line": 33, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 33, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1126", "line": 55, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 55, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1128", "line": 8, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 8, "endColumn": 24}, {"ruleId": "1104", "severity": 1, "message": "1129", "line": 7, "column": 7, "nodeType": "1106", "messageId": "1107", "endLine": 7, "endColumn": 10}, {"ruleId": "1104", "severity": 1, "message": "1130", "line": 1, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 16}, {"ruleId": "1104", "severity": 1, "message": "1108", "line": 2, "column": 21, "nodeType": "1106", "messageId": "1107", "endLine": 2, "endColumn": 28}, {"ruleId": "1104", "severity": 1, "message": "1131", "line": 4, "column": 31, "nodeType": "1106", "messageId": "1107", "endLine": 4, "endColumn": 52}, {"ruleId": "1104", "severity": 1, "message": "1132", "line": 8, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 8, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1133", "line": 23, "column": 11, "nodeType": "1106", "messageId": "1107", "endLine": 23, "endColumn": 18}, {"ruleId": "1104", "severity": 1, "message": "1134", "line": 24, "column": 11, "nodeType": "1106", "messageId": "1107", "endLine": 24, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1135", "line": 25, "column": 11, "nodeType": "1106", "messageId": "1107", "endLine": 25, "endColumn": 18}, {"ruleId": "1104", "severity": 1, "message": "1136", "line": 16, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 16, "endColumn": 24}, {"ruleId": "1104", "severity": 1, "message": "1137", "line": 1, "column": 17, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 27}, {"ruleId": "1104", "severity": 1, "message": "1138", "line": 3, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 3, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1123", "line": 25, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 25, "endColumn": 17}, {"ruleId": "1104", "severity": 1, "message": "1139", "line": 2, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 2, "endColumn": 20}, {"ruleId": "1104", "severity": 1, "message": "1108", "line": 12, "column": 21, "nodeType": "1106", "messageId": "1107", "endLine": 12, "endColumn": 28}, {"ruleId": "1104", "severity": 1, "message": "1140", "line": 1, "column": 42, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 51}, {"ruleId": "1104", "severity": 1, "message": "1113", "line": 1, "column": 16, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 20}, {"ruleId": "1104", "severity": 1, "message": "1141", "line": 3, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 3, "endColumn": 8}, {"ruleId": "1104", "severity": 1, "message": "1140", "line": 3, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 3, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1142", "line": 3, "column": 21, "nodeType": "1106", "messageId": "1107", "endLine": 3, "endColumn": 30}, {"ruleId": "1104", "severity": 1, "message": "1143", "line": 3, "column": 32, "nodeType": "1106", "messageId": "1107", "endLine": 3, "endColumn": 46}, {"ruleId": "1104", "severity": 1, "message": "1144", "line": 3, "column": 48, "nodeType": "1106", "messageId": "1107", "endLine": 3, "endColumn": 57}, {"ruleId": "1104", "severity": 1, "message": "1145", "line": 3, "column": 59, "nodeType": "1106", "messageId": "1107", "endLine": 3, "endColumn": 67}, {"ruleId": "1104", "severity": 1, "message": "1146", "line": 3, "column": 69, "nodeType": "1106", "messageId": "1107", "endLine": 3, "endColumn": 74}, {"ruleId": "1104", "severity": 1, "message": "1147", "line": 4, "column": 16, "nodeType": "1106", "messageId": "1107", "endLine": 4, "endColumn": 27}, {"ruleId": "1104", "severity": 1, "message": "1148", "line": 4, "column": 39, "nodeType": "1106", "messageId": "1107", "endLine": 4, "endColumn": 45}, {"ruleId": "1104", "severity": 1, "message": "1149", "line": 4, "column": 47, "nodeType": "1106", "messageId": "1107", "endLine": 4, "endColumn": 58}, {"ruleId": "1104", "severity": 1, "message": "1150", "line": 4, "column": 60, "nodeType": "1106", "messageId": "1107", "endLine": 4, "endColumn": 73}, {"ruleId": "1104", "severity": 1, "message": "1151", "line": 4, "column": 75, "nodeType": "1106", "messageId": "1107", "endLine": 4, "endColumn": 88}, {"ruleId": "1104", "severity": 1, "message": "1152", "line": 5, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 5, "endColumn": 13}, {"ruleId": "1104", "severity": 1, "message": "1153", "line": 5, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 5, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1154", "line": 5, "column": 23, "nodeType": "1106", "messageId": "1107", "endLine": 5, "endColumn": 30}, {"ruleId": "1104", "severity": 1, "message": "1155", "line": 30, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 30, "endColumn": 14}, {"ruleId": "1104", "severity": 1, "message": "1156", "line": 1, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 22}, {"ruleId": "1104", "severity": 1, "message": "1157", "line": 14, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 14, "endColumn": 26}, {"ruleId": "1104", "severity": 1, "message": "1156", "line": 1, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 22}, {"ruleId": "1104", "severity": 1, "message": "1158", "line": 17, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 17, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1109", "line": 67, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 67, "endColumn": 24}, {"ruleId": "1104", "severity": 1, "message": "1159", "line": 6, "column": 11, "nodeType": "1106", "messageId": "1107", "endLine": 6, "endColumn": 20}, {"ruleId": "1104", "severity": 1, "message": "1160", "line": 17, "column": 39, "nodeType": "1106", "messageId": "1107", "endLine": 17, "endColumn": 50}, {"ruleId": "1104", "severity": 1, "message": "1161", "line": 21, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 21, "endColumn": 20}, {"ruleId": "1104", "severity": 1, "message": "1162", "line": 2, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 2, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1163", "line": 1, "column": 14, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 18}, {"ruleId": "1104", "severity": 1, "message": "1156", "line": 1, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 22}, {"ruleId": "1104", "severity": 1, "message": "1143", "line": 8, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 8, "endColumn": 17}, {"ruleId": "1104", "severity": 1, "message": "1146", "line": 11, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 11, "endColumn": 8}, {"ruleId": "1104", "severity": 1, "message": "1164", "line": 67, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 67, "endColumn": 15}, {"ruleId": "1104", "severity": 1, "message": "1165", "line": 113, "column": 11, "nodeType": "1106", "messageId": "1107", "endLine": 113, "endColumn": 31}, {"ruleId": "1104", "severity": 1, "message": "1166", "line": 8, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 8, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1167", "line": 75, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 75, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1165", "line": 283, "column": 11, "nodeType": "1106", "messageId": "1107", "endLine": 283, "endColumn": 31}, {"ruleId": "1104", "severity": 1, "message": "1168", "line": 422, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 422, "endColumn": 22}, {"ruleId": "1104", "severity": 1, "message": "1143", "line": 9, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 9, "endColumn": 17}, {"ruleId": "1104", "severity": 1, "message": "1146", "line": 12, "column": 3, "nodeType": "1106", "messageId": "1107", "endLine": 12, "endColumn": 8}, {"ruleId": "1104", "severity": 1, "message": "1165", "line": 98, "column": 11, "nodeType": "1106", "messageId": "1107", "endLine": 98, "endColumn": 31}, {"ruleId": "1104", "severity": 1, "message": "1169", "line": 11, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 11, "endColumn": 25}, {"ruleId": "1104", "severity": 1, "message": "1170", "line": 24, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 24, "endColumn": 23}, {"ruleId": "1104", "severity": 1, "message": "1171", "line": 25, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 25, "endColumn": 24}, {"ruleId": "1104", "severity": 1, "message": "1172", "line": 28, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 28, "endColumn": 17}, {"ruleId": "1104", "severity": 1, "message": "1173", "line": 1, "column": 36, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 43}, {"ruleId": "1104", "severity": 1, "message": "1174", "line": 5, "column": 22, "nodeType": "1106", "messageId": "1107", "endLine": 5, "endColumn": 33}, {"ruleId": "1104", "severity": 1, "message": "1174", "line": 10, "column": 23, "nodeType": "1106", "messageId": "1107", "endLine": 10, "endColumn": 34}, {"ruleId": "1104", "severity": 1, "message": "1175", "line": 34, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 34, "endColumn": 19}, {"ruleId": "1104", "severity": 1, "message": "1137", "line": 14, "column": 38, "nodeType": "1106", "messageId": "1107", "endLine": 14, "endColumn": 48}, {"ruleId": "1104", "severity": 1, "message": "1138", "line": 18, "column": 10, "nodeType": "1106", "messageId": "1107", "endLine": 18, "endColumn": 21}, {"ruleId": "1104", "severity": 1, "message": "1176", "line": 412, "column": 25, "nodeType": "1106", "messageId": "1107", "endLine": 412, "endColumn": 32}, {"ruleId": "1104", "severity": 1, "message": "1177", "line": 417, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 417, "endColumn": 33}, {"ruleId": "1104", "severity": 1, "message": "1178", "line": 419, "column": 15, "nodeType": "1106", "messageId": "1107", "endLine": 419, "endColumn": 31}, {"ruleId": "1104", "severity": 1, "message": "1170", "line": 23, "column": 12, "nodeType": "1106", "messageId": "1107", "endLine": 23, "endColumn": 25}, {"ruleId": "1104", "severity": 1, "message": "1179", "line": 1, "column": 27, "nodeType": "1106", "messageId": "1107", "endLine": 1, "endColumn": 36}, {"ruleId": "1104", "severity": 1, "message": "1180", "line": 33, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 33, "endColumn": 28}, {"ruleId": "1104", "severity": 1, "message": "1181", "line": 56, "column": 9, "nodeType": "1106", "messageId": "1107", "endLine": 56, "endColumn": 23}, {"ruleId": "1104", "severity": 1, "message": "1133", "line": 13, "column": 11, "nodeType": "1106", "messageId": "1107", "endLine": 13, "endColumn": 18}, {"ruleId": "1104", "severity": 1, "message": "1182", "line": 23, "column": 8, "nodeType": "1106", "messageId": "1107", "endLine": 23, "endColumn": 18}, "no-unused-vars", "'userRole' is assigned a value but never used.", "Identifier", "unusedVar", "'element' is defined but never used.", "'slotController' is assigned a value but never used.", "'Permission' is defined but never used.", "'Can' is defined but never used.", "'ROLES' is defined but never used.", "'Grid' is defined but never used.", "'users' is assigned a value but never used.", "'DialogContentText' is defined but never used.", "'daysOfMonth' is assigned a value but never used.", "'FilterBox' is assigned a value but never used.", "'history' is assigned a value but never used.", "'totalSpent' is assigned a value but never used.", "'ErrorOutlineIcon' is defined but never used.", "'Divider' is defined but never used.", "'currentUser' is assigned a value but never used.", "'products' is assigned a value but never used.", "'isMySprintPage' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'result' is assigned a value but never used.", "'data' is assigned a value but never used.", "'SettingSelector' is defined but never used.", "'log' is assigned a value but never used.", "'values' is defined but never used.", "'AssignmentIndOutlined' is defined but never used.", "'LeaveActions' is defined but never used.", "'profile' is assigned a value but never used.", "'countLeave' is assigned a value but never used.", "'setting' is assigned a value but never used.", "'upcomingLeaves' is assigned a value but never used.", "'useContext' is defined but never used.", "'backContext' is defined but never used.", "'PlayCircle' is defined but never used.", "'TableBody' is defined but never used.", "'Table' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Paper' is defined but never used.", "'ButtonGroup' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'IconButton' is defined but never used.", "'Popper' is defined but never used.", "'Popover' is defined but never used.", "'theme' is assigned a value but never used.", "'API_BASE_URL' is defined but never used.", "'queryParams' is assigned a value but never used.", "'CircularProgress' is defined but never used.", "'InputBase' is defined but never used.", "'UserActions' is defined but never used.", "'LeaveStatus' is defined but never used.", "'getApiUrl' is defined but never used.", "'post' is defined but never used.", "'totals' is assigned a value but never used.", "'formatTimeForTooltip' is assigned a value but never used.", "'monthData' is assigned a value but never used.", "'getMonthName' is assigned a value but never used.", "'monthlyTotals' is assigned a value but never used.", "'ActivityActions' is defined but never used.", "'todayActivity' is assigned a value but never used.", "'todayHistoryArr' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'useMemo' is defined but never used.", "'useSelector' is defined but never used.", "'body' is assigned a value but never used.", "'endHour' is assigned a value but never used.", "'startTimeFormatted' is assigned a value but never used.", "'endTimeFormatted' is assigned a value but never used.", "'useEffect' is defined but never used.", "'getCurrentDateRange' is assigned a value but never used.", "'lastFetchedRef' is assigned a value but never used.", "'formatTime' is assigned a value but never used."]