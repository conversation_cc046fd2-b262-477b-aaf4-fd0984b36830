const mongoose = require('mongoose');

console.log('Starting workSchedule migration...');

async function migrate() {
  try {
    await mongoose.connect('mongodb://localhost:27017/meraki');
    console.log('Connected to MongoDB');

    // Create a simple schema for migration
    const UserSchema = new mongoose.Schema({}, { strict: false });
    const User = mongoose.model('User', UserSchema);

    // Find all users with string workSchedule
    const users = await User.find({
      $or: [
        { workSchedule: { $type: "string" } },
        { workSchedule: { $exists: false } },
        { workSchedule: null }
      ]
    });

    console.log(`Found ${users.length} users to migrate`);

    let updated = 0;
    for (const user of users) {
      await User.updateOne(
        { _id: user._id },
        {
          $set: {
            workSchedule: {
              scheduleTemplate: 'day_shift',
              shiftStart: new Date('2025-06-11'),
              shiftEnd: new Date('2025-06-11'),
              startTime: '09:00',
              endTime: '17:00',
              minimumHours: 8.0
            }
          }
        }
      );
      updated++;
      console.log(`Updated user: ${user.name || user.email || user._id}`);
    }

    console.log(`Migration completed! Updated ${updated} users`);
    await mongoose.connection.close();
    console.log('Database connection closed');

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

migrate();
